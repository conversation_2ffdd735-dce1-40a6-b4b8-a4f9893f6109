#!/bin/bash

echo "Building debug tools..."

# Compile interface debug tool
echo "Compiling debug_interface..."
gcc -o debug_interface debug_interface.c
if [ $? -eq 0 ]; then
    echo "✅ debug_interface compiled successfully"
else
    echo "❌ debug_interface compilation failed"
    exit 1
fi

# Compile ring buffer debug tool
echo "Compiling debug_ring_buffer..."
gcc -o debug_ring_buffer debug_ring_buffer.c
if [ $? -eq 0 ]; then
    echo "✅ debug_ring_buffer compiled successfully"
else
    echo "❌ debug_ring_buffer compilation failed"
    exit 1
fi

echo ""
echo "Debug tools ready!"
echo "=================="
echo "1. ./debug_interface [interface_name] - Test interface and packet capture"
echo "2. ./debug_ring_buffer - Test PACKET_MMAP parameters"
echo ""
echo "Example usage:"
echo "  ./debug_interface eth0"
echo "  ./debug_interface 1.x3"
echo ""
