#!/bin/bash

echo "Building debug tools..."

# Compile interface debug tool
echo "Compiling debug_interface..."
gcc -o debug_interface debug_interface.c
if [ $? -eq 0 ]; then
    echo "✅ debug_interface compiled successfully"
else
    echo "❌ debug_interface compilation failed"
    exit 1
fi

# Compile ring buffer debug tool
echo "Compiling debug_ring_buffer..."
gcc -o debug_ring_buffer debug_ring_buffer.c
if [ $? -eq 0 ]; then
    echo "✅ debug_ring_buffer compiled successfully"
else
    echo "❌ debug_ring_buffer compilation failed"
    exit 1
fi

# Compile ring buffer test tool
echo "Compiling test_ring_buffer..."
gcc -o test_ring_buffer test_ring_buffer.c
if [ $? -eq 0 ]; then
    echo "✅ test_ring_buffer compiled successfully"
else
    echo "❌ test_ring_buffer compilation failed"
    exit 1
fi

echo ""
echo "Debug tools ready!"
echo "=================="
echo "1. ./debug_interface [interface_name] - Test interface and packet capture"
echo "2. ./debug_ring_buffer - Test PACKET_MMAP parameters"
echo "3. ./test_ring_buffer [interface_name] - Direct ring buffer test"
echo ""
echo "Example usage:"
echo "  ./debug_interface 1.x3"
echo "  ./test_ring_buffer 1.x3"
echo ""
echo "Recommended testing order:"
echo "1. First run: ./test_ring_buffer 1.x3"
echo "2. If that works, the issue is in pktlib integration"
echo "3. If that fails, the issue is in ring buffer setup"
echo ""
