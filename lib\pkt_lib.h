#ifndef __pkt_lib_h__
#define __pkt_lib_h__

#define IFNAME_SIZE 40
#define ETH_ALEN    6

typedef enum {
	PKTLIB_PROTO_RSTP   = 0x1 << 0,
	PKTLIB_PROTO_G8032  = 0x1 << 1,
	PKTLIB_PROTO_ERPS   = 0x1 << 2,
	PKTLIB_PROTO_LACP   = 0x1 << 3,
	PKTLIB_PROTO_LLDP   = 0x1 << 4,
	PKTLIB_PROTO_8021X  = 0x1 << 5,
	PKTLIB_PROTO_8021AG = 0x1 << 6,
	PKTLIB_PROTO_PPP    = 0x1 << 7,
	PKTLIB_PROTO_ARP    = 0x1 << 8,
	PKTLIB_PROTO_DHCPV4 = 0x1 << 9,
	PKTLIB_PROTO_DHCPV6 = 0x1 << 10,
	PKTLIB_PROTO_ICMPV6 = 0x1 << 11,
	PKTLIB_PROTO_MAX
} pktlib_protocol_e;

typedef enum {
	PKTLIB_DIR_INGRESS   = 0,
	PKTLIB_DIR_EGRESS    = 1,
    PKTLIB_DIR_BIDIRECTION = 2
} pktlib_direction_e;

#define PKT_LEN_MAX      2048
typedef struct {
    unsigned short tci;
    unsigned short etherType;
} __attribute__((packed)) pkt_vlanHeader_t;


typedef struct {
    unsigned char mac_dest[ETH_ALEN];
    unsigned char mac_source[ETH_ALEN];
    unsigned short etherType;
    pkt_vlanHeader_t vlanHeader;
} __attribute__((packed)) pkt_ethHeader_t;

typedef struct {
#if defined(__BIG_ENDIAN__) 
    unsigned char  version:4;
    unsigned char  ihl:4;
#else
    unsigned char  ihl:4;
    unsigned char  version:4;
#endif
    unsigned char  tos;
    unsigned short totalLength;
    unsigned short id;
    unsigned short flags_fragOffset;
    unsigned char  ttl;
    unsigned char  protocol;
    unsigned short headerChecksum;
    unsigned int ipSrc;
    unsigned int ipDest;
} __attribute__((packed)) pkt_ipHeader_t;

/*
 *	IPv6 fixed header
 *
 *	BEWARE, it is incorrect. The first 4 bits of flow_lbl
 *	are glued to priority now, forming "class".
 */
typedef struct {
#if defined(__BIG_ENDIAN__) 
    unsigned char  version:4;
    unsigned char  tos:4;
#else
    unsigned char  tos:4;
    unsigned char  version:4;
#endif
    unsigned char  flowLabel[3];
    unsigned short totalLength;
    unsigned char  nh;
    unsigned char  ttl;
    unsigned int ipSrc[4];
    unsigned int ipDest[4];
} __attribute__((packed)) pkt_ipv6Header_t;

typedef struct {
    unsigned char version_type;
    unsigned char code;
    unsigned short sessionId;
    unsigned short length;
    unsigned short pppHeader;
    pkt_ipHeader_t ipHeader;
} __attribute__((packed)) pkt_pppoeSessionHeader_t;

typedef struct {
	unsigned char  type;
	unsigned char  code;		/* For newer IGMP */
	unsigned short csum;
	unsigned int   group;
} __attribute__((packed)) pkt_igmpHeader_t;

typedef struct {
	unsigned short source;
	unsigned short dest;
	unsigned short len;
	unsigned short check;
} __attribute__((packed)) pkt_udpHeader_t;

/* packet metadata */
typedef struct {
    unsigned short data_len;
    unsigned char pkt[PKT_LEN_MAX]; /* received packet */

	/* source interface */
    unsigned short ifindex;
    char ifname[IFNAME_SIZE];

	/* L2 information */
    pkt_ethHeader_t *ethHeader;
	unsigned char  mac_dest[ETH_ALEN];
	unsigned char  mac_source[ETH_ALEN];
    unsigned short etherType;
	unsigned short outer_vlan_id;
	unsigned short outer_pcp;
	unsigned short inner_vlan_id;
	unsigned short inner_pcp;

	/* NON IP header */
    unsigned char *non_ip_header;
	
	/* IP header */
    pkt_ipHeader_t *ipHeader;

	/* L4 header */
    pkt_igmpHeader_t *igmpHeader;
    pkt_udpHeader_t  *udpHeader;
    /* other protocol header */
    unsigned char      *l4Header;
} pktlib_pkt_info;

/* protocol DM call back function */
typedef int (*rx_handler_func_t)(pktlib_protocol_e protocol, 
	char *interface, int vlan, pktlib_pkt_info *pkt_info);

typedef struct {
	pktlib_protocol_e protocol;    /* protocol */
    pktlib_direction_e direction;  /* 0:ingress 1:egress 2：bidirection*/
	char interface[IFNAME_SIZE];   /* physical interface */
	unsigned char dst_mac[6];      /* for 802.1ag only */
	int vlan_id;
    /* Indicates whether the socket's polling should be handled externally by the caller 
        0: the library will automatically include the socket in its internal polling mechanism
        1: the library will not poll the socket. It is the caller's responsibility to monitor the socket for events
    */
    int external_polling;
    int handle;                    /* returned by socket creation, used when sending packet */
	rx_handler_func_t rx_handler;  /* This rx_handler is only required and used when `external_polling` is set to `0`*/
} pktlib_protocol_filter_rule;

typedef struct {
	unsigned int  tpid:16,	/* tag protocol identifier e.g. 0x8100 or 0x88a8 */ 
		          pri:3,	/* priority bits 0 low 7 hi */
			      cfi:1,	/* cannonical format indicator used as drop eligible indicator */
			      vlan:12; 	/* vlan id 1-4095 */
} pktlib_vlan_tci_t;

typedef struct {
	unsigned char pkt_data[PKT_LEN_MAX];  /* send packet, including IP header and MAC header */
	unsigned short pkt_len;               /* packet length */
    unsigned short num_vlan_tags;         /* number of tags present in packet */
    pktlib_vlan_tci_t svid;               /* vlan id 1.need LIB to do VLAN TAG 2. want to flood packet in the VLAN domain */
    pktlib_vlan_tci_t cvid;               /* inner vlan */
	int handle;                           /* related handle */
    char dst_interface[IFNAME_SIZE];      /* dest interface */
	char src_interface[IFNAME_SIZE];      /* source interface, if you want to flood the packets in VLAN domain and exclude the source interface */
} pktlib_protocol_meta;

/*
    \brief Register protocol to pkt-lib with protocol code, vlan, interface ...

    This function register protocol to pkt-lib to create socket to receive and transmit packet

    \param[in] filter_rule  protocol filter information

    \return
    0 on success<br>
    non-zero on failure
*/
int pktlib_register(pktlib_protocol_filter_rule *filter_rule);

/*
    \brief Unregister protocol to pkt-lib with protocol code, vlan, interface ...

    This function unregister protocol to pkt-lib

    \param[in] filter_rule  protocol filter information

    \return
    0 on success<br>
    non-zero on failure
*/
int pktlib_unregister(pktlib_protocol_filter_rule *filter_rule);

/*
    \brief Use pkt-lib to send packet

    This function send packet to a specify port or flood in a VLAN bridge

    \param[in] meta send packet information

    \return
    0 on success<br>
    non-zero on failure
*/
int pktlib_xmit(pktlib_protocol_meta *meta);

/*
    \brief Clean up pktlib resources including ring buffers

    This function should be called before program exit to free all allocated resources
*/
void pktlib_cleanup(void);

/*
    \brief Get performance statistics for a protocol

    \param[in] filter_rule  protocol filter information to identify the socket
    \param[out] pps         packets per second (can be NULL)
    \param[out] mbps        megabits per second (can be NULL)

    \return
    0 on success<br>
    non-zero on failure
*/
int pktlib_get_performance(pktlib_protocol_filter_rule *filter_rule, double *pps, double *mbps);

/*
    \brief Print performance statistics for all registered protocols
*/
void pktlib_print_performance(void);

#endif	/* __pkt_lib_h__ */
