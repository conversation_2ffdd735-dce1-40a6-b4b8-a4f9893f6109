/*
 * Simple performance test for pktlib - focuses on basic optimizations
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <signal.h>
#include <time.h>
#include <pkt_lib.h>

static volatile int running = 1;
static unsigned long packet_count = 0;
static unsigned long byte_count = 0;
static struct timespec start_time;

void signal_handler(int sig)
{
    running = 0;
}

/* Simple packet handler that just counts packets */
static int simple_rx_handler(pktlib_protocol_e protocol, 
                             char *interface, int vlan, pktlib_pkt_info *pkt_info)
{
    if (pkt_info) {
        packet_count++;
        byte_count += pkt_info->data_len;
        
        /* Print progress every 10000 packets */
        if ((packet_count % 10000) == 0) {
            struct timespec now;
            clock_gettime(CLOCK_MONOTONIC, &now);
            double elapsed = (now.tv_sec - start_time.tv_sec) + 
                           (now.tv_nsec - start_time.tv_nsec) / 1e9;
            
            if (elapsed > 0) {
                printf("Progress: %lu packets, %.2f PPS, %.2f Mbps\n",
                       packet_count, packet_count / elapsed, 
                       (byte_count * 8.0) / (elapsed * 1e6));
            }
        }
    }
    return 0;
}

int main(int argc, char *argv[])
{
    char interface[64] = "eth0";
    int test_duration = 30;
    
    if (argc > 1) {
        strncpy(interface, argv[1], sizeof(interface) - 1);
    }
    if (argc > 2) {
        test_duration = atoi(argv[2]);
    }
    
    printf("Simple PKTLIB Performance Test\n");
    printf("==============================\n");
    printf("Interface: %s\n", interface);
    printf("Duration: %d seconds\n", test_duration);
    printf("Testing ARP packet processing...\n\n");
    
    /* Set up signal handler */
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    
    /* Register for ARP packets */
    pktlib_protocol_filter_rule filter_rule = {0};
    filter_rule.protocol = PKTLIB_PROTO_ARP;  /* ARP packets */
    filter_rule.direction = PKTLIB_DIR_INGRESS;
    strncpy(filter_rule.interface, interface, sizeof(filter_rule.interface) - 1);
    filter_rule.vlan_id = 0;
    filter_rule.external_polling = 0;
    filter_rule.rx_handler = simple_rx_handler;
    
    clock_gettime(CLOCK_MONOTONIC, &start_time);
    
    int ret = pktlib_register(&filter_rule);
    if (ret != 0) {
        printf("Failed to register ARP protocol: %d\n", ret);
        return 1;
    }
    
    printf("ARP protocol registered, handle: %d\n", filter_rule.handle);
    printf("Capturing packets... (Press Ctrl+C to stop)\n\n");
    
    /* Main loop */
    struct timespec last_print = start_time;
    while (running) {
        sleep(1);
        
        struct timespec now;
        clock_gettime(CLOCK_MONOTONIC, &now);
        double elapsed = (now.tv_sec - start_time.tv_sec) + 
                        (now.tv_nsec - start_time.tv_nsec) / 1e9;
        
        /* Print stats every 5 seconds */
        double print_elapsed = (now.tv_sec - last_print.tv_sec) + 
                              (now.tv_nsec - last_print.tv_nsec) / 1e9;
        
        if (print_elapsed >= 5.0) {
            if (elapsed > 0) {
                printf("Time: %.1fs, Packets: %lu, PPS: %.2f, Mbps: %.2f\n",
                       elapsed, packet_count, packet_count / elapsed,
                       (byte_count * 8.0) / (elapsed * 1e6));
            }
            last_print = now;
        }
        
        if (elapsed >= test_duration) {
            break;
        }
    }
    
    /* Final results */
    struct timespec end_time;
    clock_gettime(CLOCK_MONOTONIC, &end_time);
    double total_time = (end_time.tv_sec - start_time.tv_sec) + 
                       (end_time.tv_nsec - start_time.tv_nsec) / 1e9;
    
    printf("\n=== Test Results ===\n");
    printf("Total time: %.2f seconds\n", total_time);
    printf("Total packets: %lu\n", packet_count);
    printf("Total bytes: %lu\n", byte_count);
    
    if (total_time > 0) {
        printf("Average PPS: %.2f\n", packet_count / total_time);
        printf("Average Mbps: %.2f\n", (byte_count * 8.0) / (total_time * 1e6));
        
        /* Performance assessment */
        double mbps = (byte_count * 8.0) / (total_time * 1e6);
        printf("\nPerformance Assessment:\n");
        if (mbps > 800) {
            printf("✅ EXCELLENT: >800 Mbps - Optimization successful!\n");
        } else if (mbps > 400) {
            printf("✅ GOOD: >400 Mbps - Significant improvement\n");
        } else if (mbps > 150) {
            printf("⚠️  MODERATE: >150 Mbps - Some improvement\n");
        } else {
            printf("❌ POOR: <150 Mbps - Optimization may have failed\n");
        }
    }
    
    /* Cleanup */
    pktlib_unregister(&filter_rule);
    pktlib_cleanup();
    
    printf("\nTest completed.\n");
    return 0;
}
