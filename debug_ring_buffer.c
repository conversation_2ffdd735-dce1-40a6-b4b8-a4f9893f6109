/*
 * Debug tool to test PACKET_MMAP ring buffer parameters
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <errno.h>
#include <sys/socket.h>
#include <linux/if_packet.h>
#include <linux/if_ether.h>
#include <net/if.h>

void test_ring_params(void)
{
    int sockfd;
    struct tpacket_req req;
    int page_size = getpagesize();
    
    printf("System page size: %d bytes\n", page_size);
    printf("Testing PACKET_MMAP parameters...\n\n");
    
    /* Create socket */
    sockfd = socket(AF_PACKET, SOCK_RAW, htons(ETH_P_ALL));
    if (sockfd < 0) {
        printf("Failed to create socket: %s\n", strerror(errno));
        return;
    }
    
    /* Test different parameter combinations */
    struct {
        unsigned int block_size;
        unsigned int frame_size;
        unsigned int block_nr;
        const char *description;
    } test_params[] = {
        {page_size, page_size/4, 16, "Conservative: page/4 frame, 16 blocks"},
        {page_size, page_size/2, 32, "Moderate: page/2 frame, 32 blocks"},
        {page_size*2, page_size, 16, "Large: page frame, 2*page blocks"},
        {page_size, 128, 64, "Small frames: 128 byte frames"},
        {page_size*4, 2048, 32, "Original attempt: 2048 byte frames"},
        {0, 0, 0, NULL}
    };
    
    for (int i = 0; test_params[i].description; i++) {
        printf("Test %d: %s\n", i+1, test_params[i].description);
        
        req.tp_block_size = test_params[i].block_size;
        req.tp_frame_size = test_params[i].frame_size;
        req.tp_block_nr = test_params[i].block_nr;
        req.tp_frame_nr = req.tp_block_nr * req.tp_block_size / req.tp_frame_size;
        
        printf("  block_size=%u, frame_size=%u, block_nr=%u, frame_nr=%u\n",
               req.tp_block_size, req.tp_frame_size, req.tp_block_nr, req.tp_frame_nr);
        
        /* Validate basic requirements */
        if (req.tp_block_size % page_size != 0) {
            printf("  ❌ FAIL: block_size not multiple of page_size\n\n");
            continue;
        }
        
        if (req.tp_frame_size == 0 || req.tp_block_size % req.tp_frame_size != 0) {
            printf("  ❌ FAIL: block_size not multiple of frame_size\n\n");
            continue;
        }
        
        if (req.tp_frame_nr == 0) {
            printf("  ❌ FAIL: calculated frame_nr is 0\n\n");
            continue;
        }
        
        /* Try to set up RX ring */
        if (setsockopt(sockfd, SOL_PACKET, PACKET_RX_RING, &req, sizeof(req)) < 0) {
            printf("  ❌ FAIL: setsockopt PACKET_RX_RING failed: %s\n\n", strerror(errno));
            continue;
        }
        
        printf("  ✅ SUCCESS: RX ring setup successful\n");
        printf("  Total memory: %zu bytes\n", (size_t)req.tp_block_size * req.tp_block_nr);
        
        /* Clean up ring */
        struct tpacket_req zero_req = {0};
        setsockopt(sockfd, SOL_PACKET, PACKET_RX_RING, &zero_req, sizeof(zero_req));
        printf("\n");
    }
    
    close(sockfd);
}

void print_system_info(void)
{
    printf("System Information:\n");
    printf("==================\n");
    
    /* Check kernel version */
    FILE *fp = fopen("/proc/version", "r");
    if (fp) {
        char version[256];
        if (fgets(version, sizeof(version), fp)) {
            printf("Kernel: %s", version);
        }
        fclose(fp);
    }
    
    /* Check memory info */
    fp = fopen("/proc/meminfo", "r");
    if (fp) {
        char line[256];
        while (fgets(line, sizeof(line), fp)) {
            if (strncmp(line, "MemTotal:", 9) == 0 ||
                strncmp(line, "MemFree:", 8) == 0 ||
                strncmp(line, "MemAvailable:", 13) == 0) {
                printf("%s", line);
            }
        }
        fclose(fp);
    }
    
    /* Check network buffer limits */
    fp = fopen("/proc/sys/net/core/rmem_max", "r");
    if (fp) {
        int rmem_max;
        if (fscanf(fp, "%d", &rmem_max) == 1) {
            printf("net.core.rmem_max: %d bytes (%.2f MB)\n", rmem_max, rmem_max / 1e6);
        }
        fclose(fp);
    }
    
    fp = fopen("/proc/sys/net/core/wmem_max", "r");
    if (fp) {
        int wmem_max;
        if (fscanf(fp, "%d", &wmem_max) == 1) {
            printf("net.core.wmem_max: %d bytes (%.2f MB)\n", wmem_max, wmem_max / 1e6);
        }
        fclose(fp);
    }
    
    printf("\n");
}

int main(int argc, char *argv[])
{
    printf("PKTLIB Ring Buffer Debug Tool\n");
    printf("=============================\n\n");
    
    print_system_info();
    test_ring_params();
    
    printf("Debug completed. Use successful parameters in pktlib configuration.\n");
    return 0;
}
