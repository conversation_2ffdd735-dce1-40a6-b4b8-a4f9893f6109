/*
 * *************************************************************************************
 *
 *       Filename:  pktlib_main.c
 *
 *    Description:  Template
 *
 *        Version:  1.0
 *        Created:  04/09/2025 02:08:37 AM
 *       Revision:  none
 *       Compiler:  gcc
 *
 *         Author:  jane gong, <EMAIL>
 *   Organization:  Calix
 *
 * *************************************************************************************
 */

#define _GNU_SOURCE
#include <stdlib.h>
#include <sys/shm.h>
#include <sys/mman.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <sys/inotify.h>
#include <fcntl.h>
#include <sys/ioctl.h>
#include <string.h>
#include <stdbool.h>
#include <stdio.h>
#include <unistd.h>
#include <errno.h>
#include <semaphore.h>
#include <regex.h>
#include <libgen.h>
#include <pthread.h>
#include <daemonlib.h>
#include <cmd_parser.h>
#include <signal.h>
#include <sl_files.h>
#include <dl_util.h>
#include <ipc/ipc_lib.h>
#include <ipc/ipc_msg_def.h>
#include <version.h>
#include <../lib/pkt_lib.h>
#include <sys/socket.h>
#include <linux/if_packet.h>
typedef struct test_pktlib_ctx_t {
    dl_context_t               *mydaemon;
    dl_server_t                *myserver;
    cp_context_t               *dcli_cp;
    sl_schema_set_t            *schema;
    char                       myname[DL_PROCESS_NAME_LEN + 1];
    char                       logfile[256];
    uint8_t                    flags;
    dl_timer                   mytimer;
} test_pktlib_ctx_t;

/* save vlan and interface info */
typedef struct test_pktlib_proto_info {
    char reg_if[40];
    int reg_vlan_id;
    int reg_proto;
    int reg_external_polling;
    char xmit_dst_if[40];
    char xmit_src_if[40];
    int xmit_vlan_num;
    int xmit_vlan_tpid;
    int xmit_vlan_id;
    int xmit_vlan_pcp;
    int handle;
    struct test_pktlib_proto_info *next;
} test_pktlib_proto_info;

static test_pktlib_ctx_t tmp_mgr_ctx;
static test_pktlib_ctx_t* p_root_ctx = &tmp_mgr_ctx;
static test_pktlib_proto_info *proto_list_head = NULL;
#if 0
#ifndef IPC_BUF_SZ
#define IPC_BUF_SZ   0xA00000
#endif
#endif

/**
 * Defines and macros.                                                                             *
 */
#define _DM_FORCE_START_          0x00000001
#define _DM_FOREGROUND_           0x00000002
#define _DM_DO_RESTART_           0x00000004
#define _DM_DO_SHUTDOWN_          0x00000008
#define _DM_INIT_RUN_             0x00000010

void usage(void) {
}

/* add proto info to list */
static void pkt_add_proto_info(test_pktlib_proto_info *proto)
{
    test_pktlib_proto_info *new_proto = (test_pktlib_proto_info *)malloc(sizeof(test_pktlib_proto_info));
    if (new_proto == NULL) {
        dl_log_print(ILOG_ERR, "malloc proto info fail");
        return;
    }

    memcpy(new_proto, proto, sizeof(test_pktlib_proto_info));
    new_proto->next = proto_list_head;
    proto_list_head = new_proto;
    dl_log_print(ILOG_DEBUG, "added proto :reg_if %s reg_vlan_id %d reg_proto %d xmit_dst_if %s xmit_src_if %s xmit_vlan_num %d "
        "xmit_vlan_tpid %d xmit_vlan_id %d xmit_vlan_pcp %d handle %d\n", 
        new_proto->reg_if, new_proto->reg_vlan_id, new_proto->reg_proto, new_proto->xmit_dst_if, 
        new_proto->xmit_src_if, new_proto->xmit_vlan_num, new_proto->xmit_vlan_tpid, 
        new_proto->xmit_vlan_id, new_proto->xmit_vlan_pcp, new_proto->handle);

    return;
}

static void pkt_del_proto_info(const char *reg_if, int reg_vlan_id, int reg_proto)
{
    test_pktlib_proto_info *current = proto_list_head;
    test_pktlib_proto_info *prev = NULL;
    dl_log_print(ILOG_DEBUG, " pkt_del_proto_info  param reg_if %s reg_vlan_id %d reg_proto %d\n", 
        reg_if, reg_vlan_id, reg_proto);
    while (current != NULL) {
        dl_log_print(ILOG_DEBUG, " pkt_del_proto_info current proto :reg_if %s reg_vlan_id %d reg_proto %d handle %d \n", 
                current->reg_if, current->reg_vlan_id, current->reg_proto, current->handle);
        if (current->reg_vlan_id == reg_vlan_id && current->reg_proto == reg_proto
            && strncmp(current->reg_if, reg_if, sizeof(current->reg_if)) == 0) {
            if (prev == NULL) {
                proto_list_head = current->next;
            } else {
                prev->next = current->next;
            }
            dl_log_print(ILOG_DEBUG, "delete proto :reg_if %s reg_vlan_id %d reg_proto %d xmit_dst_if %s xmit_src_if %s xmit_vlan_num %d "
               " xmit_vlan_tpid %x xmit_vlan_id %d xmit_vlan_pcp %d handle %d\n", 
                current->reg_if, current->reg_vlan_id, current->reg_proto, current->xmit_dst_if, 
                current->xmit_src_if, current->xmit_vlan_num, current->xmit_vlan_tpid, 
                current->xmit_vlan_id, current->xmit_vlan_pcp, current->handle);
            free(current);
            return;
        }
        prev = current;
        current = current->next;
    }

    return;
}

static test_pktlib_proto_info* pkt_find_proto_info_by_misc(char *reg_if, int reg_vlan_id, int reg_proto)
{
    test_pktlib_proto_info *current = proto_list_head;
    while (current != NULL) {
        if (current->reg_vlan_id == reg_vlan_id && current->reg_proto == reg_proto 
            && strncmp(current->reg_if, reg_if, sizeof(current->reg_if)) == 0) {
            return current;
        }
        current = current->next;
    }

    return NULL;
}

static test_pktlib_proto_info* pkt_find_proto_info_by_handle(int handle)
{
    test_pktlib_proto_info *current = proto_list_head;
    while (current != NULL) {
        if (current->handle == handle)
            return current;
        current = current->next;
    }

    return NULL;
}

#if 0
/* clear proto info list  */
static void pkt_clear_proto_info()
{
    test_pktlib_proto_info *current = proto_list_head;
    test_pktlib_proto_info *temp = NULL;

    while (current != NULL) {
        temp = current;
        current = current->next;
        free(temp);
    }
    proto_list_head = NULL;
    return;
}

/* print proto list info */
static void pkt_print_proto_list()
{
    test_pktlib_proto_info *current = proto_list_head;

    while (current != NULL) {
        dl_log_print(ILOG_ERR, "delete proto :reg_if %s reg_vlan_id %d reg_proto %d xmit_dst_if %s xmit_src_if %s xmit_vlan_num %d xmit_vlan_tpid %d xmit_vlan_id %d xmit_vlan_pcp %d\n", 
            current->reg_if, current->reg_vlan_id, current->reg_proto, current->xmit_dst_if, 
            current->xmit_src_if, current->xmit_vlan_num, current->xmit_vlan_tpid, current->xmit_vlan_id, current->xmit_vlan_pcp);
        current = current->next;
    }
}
#endif
static void
dm_evt_cb(void *ctx)
{
    dl_event_t  *ev;
    int          len;
    int          events_num =  0;

    ctx = ctx;

    while ((events_num < 256) &&
            ((len = dl_event_get_next(&ev)) > 0)) {
        events_num++;
        switch (ev->type) {
        case EVENT_TYPE_POST:
            dl_log_print(ILOG_INFO, "Received event(%s)", ev->event);

            /* Call our event handler:
             */
            break;

        case EVENT_TYPE_SUBSCRIBE:
            break;

        case EVENT_TYPE_UNSUBSCRIBE:
            /* no need to do anything with unsubscribe */
            if (ev->type == EVENT_TYPE_SUBSCRIBE || ev->type == EVENT_TYPE_UNSUBSCRIBE)
                dl_log_print(ILOG_INFO, "%s for %s",
                     ev->type == EVENT_TYPE_SUBSCRIBE ? "Subscribe" : "UNsubscribe",
                     ev->event);
            break;

        default:
            break;
        }
        free(ev);
    }
}

static void dm_sig_handler(int signo)
{
    switch (signo) {
    case SIGINT:
    case SIGTERM:

        /* Graceful restart signal (expecting to be restarted by ARC).
         */
        if (p_root_ctx->flags & _DM_DO_RESTART_) {
            /* second signal, die here:
             */
            abort();
        }

        /* Set flag to exit nicely.
         */
        p_root_ctx->flags |= _DM_DO_RESTART_;
        DL_PSM_EVENT_SHUTDOWN(p_root_ctx->mydaemon);
        break;

    case SIGQUIT:

        /* Not coming back.
         */
        if (p_root_ctx->flags & _DM_DO_SHUTDOWN_) {
            abort();
        }

        /* Set flag to exit gracefully.
         */
        DL_PSM_EVENT_SHUTDOWN(p_root_ctx->mydaemon);
        break;

    case SIGHUP:
        break;

    default:
        break;
    }
}

#define HEX_BYTES_PER_LINE 16
#define HEX_CHARS_PER_BYTE 3
#define HEX_CHARS_PER_LINE (HEX_BYTES_PER_LINE * HEX_CHARS_PER_BYTE + 1)
static void pkt_dump(unsigned char *data, const unsigned int len)
{
    int i = 0, bytes = (int)len, stamp = 0;
    char line[HEX_CHARS_PER_LINE], *s;
    dl_log_print(ILOG_DEBUG, "dump %p len %d\n", data, len);
    s = line;
    while (--bytes >= 0) {
        snprintf(s, HEX_CHARS_PER_BYTE + 1, " %02X", *data++);
        s += HEX_CHARS_PER_BYTE;
        i++;
        if (i >= HEX_BYTES_PER_LINE) {
            dl_log_print(ILOG_DEBUG, "\t0x%04X: %s", stamp, line);
            i = 0;
            s = line;
            stamp += HEX_BYTES_PER_LINE;
        }
    }
    if (i) {
        *s = '\0';
        dl_log_print(ILOG_DEBUG, "\t0x%04X: %s\n", stamp, line);
    }
}

static int rx_handler(pktlib_protocol_e protocol, 
	char *interface, int vlan, pktlib_pkt_info *pkt_info)
{
    unsigned char *pkt; /* received packet */
    pktlib_protocol_meta meta = {0};
    test_pktlib_proto_info *proto;
    if (pkt_info == NULL) {
        dl_log_print(ILOG_INFO,  "rx_handler pkt_info is NULL ");
        return -1;
    }

    dl_log_print(ILOG_DEBUG, "enter rx_handler protocol %d interface %s vlan %d pkt %p data_len %d \n", 
        protocol, interface, vlan, pkt_info->pkt, pkt_info->data_len);
    dl_log_print(ILOG_DEBUG, "pktlib_pkt_info inindex %d ifname %s outer_vlan %d outer_pcp %d inner_vlan %d inner_pcp %d\n", 
        pkt_info->ifindex, pkt_info->ifname, pkt_info->outer_vlan_id, pkt_info->outer_pcp, pkt_info->inner_vlan_id, pkt_info->inner_pcp);
    pkt = pkt_info->pkt;

    pkt_dump(pkt, pkt_info->data_len);
    if (pkt_info->udpHeader) {
        pkt_udpHeader_t *udpHeader = pkt_info->udpHeader;
        dl_log_print(ILOG_DEBUG, "udp %p source %d dest %d len %d\n", udpHeader, ntohs(udpHeader->source), ntohs(udpHeader->dest), ntohs(udpHeader->len));
        //pkt_dump((unsigned char *)udpHeader, sizeof(pkt_udpHeader_t));
        //unsigned char *dhcp = (unsigned char *)pkt_info->udpHeader + sizeof(pkt_udpHeader_t);
        //dl_log_print(ILOG_INFO, "dump l4 header %p\n", dhcp);
        //pkt_dump(dhcp, ntohs(udpHeader->len));
    }
    if (pkt_info->l4Header) {
       // dl_log_print(ILOG_INFO, "non udp packet\n");
        //pkt_dump(pkt_info->l4Header, 330);
    }
    if (pkt_info->non_ip_header) {
      //  dl_log_print(ILOG_INFO, "protocol packet\n");
      //  pkt_dump(pkt_info->non_ip_header, pkt_info->data_len - 14);
    }

    /* send packet */
    proto = pkt_find_proto_info_by_misc(interface, vlan, protocol);
    if (proto) {
        memcpy(meta.pkt_data, pkt_info->pkt, sizeof(meta.pkt_data));
        meta.pkt_len = pkt_info->data_len;
        meta.num_vlan_tags = proto->xmit_vlan_num;
        meta.handle = proto->handle;
        meta.svid.tpid = proto->xmit_vlan_tpid;
        meta.svid.vlan = proto->xmit_vlan_id;
        meta.svid.pri = proto->xmit_vlan_pcp;
        strcpy(meta.dst_interface, proto->xmit_dst_if);
        strcpy(meta.src_interface, proto->xmit_src_if);
        int length = pktlib_xmit(&meta);
        dl_log_print(ILOG_DEBUG, " send packet length %d \n", length);
    }

    return 0;
}

static void external_rx_handler(int sockfd, int event, void *data)
{
    unsigned char pkt[PKT_LEN_MAX];
    pktlib_protocol_meta meta = {0};
    test_pktlib_proto_info *proto;
    struct tpacket_auxdata *tpacket;
	char cmsghdr[CMSG_SPACE(sizeof(*tpacket))];

    if (event & POLLERR) {
        dl_log_print(ILOG_ERR, "%s - Errors in pktlib rx socket. ", __FUNCTION__);
        dl_rem_pollfd(sockfd);
        close(sockfd);    //In case not closed already
        return;
    }

    if (event & POLLIN) {
		int size = 0;
        struct sockaddr_ll sockaddr = {0};
        struct msghdr msgh = {
            0,
        };
        struct iovec msg_iov = {
            0,
        };

        msg_iov.iov_base = pkt;
        msg_iov.iov_len = PKT_LEN_MAX;

        msgh.msg_name = &sockaddr;
        msgh.msg_namelen = sizeof(sockaddr);
        msgh.msg_iov = &msg_iov;
        msgh.msg_iovlen = 1;
        msgh.msg_control = &cmsghdr;
        msgh.msg_controllen = sizeof(cmsghdr);

        size = recvmsg(sockfd, &msgh, 0);
        if (size == -1) {
            dl_log_print(ILOG_ERR, "failed recv msg from socket");
            return;
        }

        if (sockaddr.sll_pkttype == PACKET_OUTGOING) {
            dl_log_print(ILOG_DEBUG, "pkttype is PACKET_OUTGOING return");
            return;
        }
        pkt_dump(pkt, size);
        /* send packet */
        proto = pkt_find_proto_info_by_handle(sockfd);
        if (proto) {
            memcpy(meta.pkt_data, pkt, sizeof(meta.pkt_data));
            meta.pkt_len = size;
            meta.num_vlan_tags = proto->xmit_vlan_num;
            meta.handle = proto->handle;
            meta.svid.tpid = proto->xmit_vlan_tpid;
            meta.svid.vlan = proto->xmit_vlan_id;
            meta.svid.pri = proto->xmit_vlan_pcp;
            strcpy(meta.dst_interface, proto->xmit_dst_if);
            strcpy(meta.src_interface, proto->xmit_src_if);
            int length = pktlib_xmit(&meta);
            dl_log_print(ILOG_DEBUG, "external_rx_handler send fd %d packet length %d \n", sockfd, length);
        }
    }

    return;
}

static int pktlib_set_register_cb(cp_context_t *cp, dl_cnx_t *cnx,
                            cp_cb_data_t *data)
{
    const char *interface = NULL;
    short idx = 0;
    uint16_t tmp;
    pktlib_protocol_filter_rule filter_rule;
    test_pktlib_proto_info proto = {0};
    int ret = 0;

	memset(&filter_rule, 0, sizeof(filter_rule));
    dl_log_print(ILOG_DEBUG, "pktlib_set_register_cb");

    if (NULL != cp_find_attr(cp, data, "interface", &idx)) {
        validate_data_get_char(&data->argt[idx], &interface);
        strncpy(filter_rule.interface, interface, sizeof(filter_rule.interface) - 1);
        strncpy(proto.reg_if, interface, sizeof(proto.reg_if) - 1);
    }

    if (NULL != cp_find_attr(cp, data, "protocol", &idx)) {
        validate_data_get_uint16(&data->argt[idx], &tmp);
        filter_rule.protocol = tmp;
        proto.reg_proto = tmp;
    }

    if (NULL != cp_find_attr(cp, data, "vlan", &idx)) {
        validate_data_get_uint16(&data->argt[idx], &tmp);
        filter_rule.vlan_id = tmp;
        proto.reg_vlan_id = tmp;
    }

    if (NULL != cp_find_attr(cp, data, "external_polling", &idx)) {
        validate_data_get_uint16(&data->argt[idx], &tmp);
        filter_rule.external_polling = tmp;
        proto.reg_external_polling = tmp;
    }

    if (NULL != cp_find_attr(cp, data, "xmit_src_if", &idx)) {
        validate_data_get_char(&data->argt[idx], &interface);
        strncpy(proto.xmit_src_if, interface, sizeof(proto.xmit_src_if) - 1);
    }

    if (NULL != cp_find_attr(cp, data, "xmit_dst_if", &idx)) {
        validate_data_get_char(&data->argt[idx], &interface);
        strncpy(proto.xmit_dst_if, interface, sizeof(proto.xmit_dst_if) - 1);
    }

    if (NULL != cp_find_attr(cp, data, "xmit_vlan_num", &idx)) {
        validate_data_get_uint16(&data->argt[idx], &tmp);
        proto.xmit_vlan_num = tmp;
    }

    if (NULL != cp_find_attr(cp, data, "xmit_vlan_tpid", &idx)) {
        validate_data_get_uint16(&data->argt[idx], &tmp);
        proto.xmit_vlan_tpid = tmp;
    }

    if (NULL != cp_find_attr(cp, data, "xmit_vlan_id", &idx)) {
        validate_data_get_uint16(&data->argt[idx], &tmp);
        proto.xmit_vlan_id = tmp;
    }

    if (NULL != cp_find_attr(cp, data, "xmit_vlan_pcp", &idx)) {
        validate_data_get_uint16(&data->argt[idx], &tmp);
        proto.xmit_vlan_pcp = tmp;
    }
    filter_rule.direction = PKTLIB_DIR_INGRESS;
    if (filter_rule.external_polling == 0)
        filter_rule.rx_handler = rx_handler;

    ret = pktlib_register(&filter_rule);
    proto.handle = filter_rule.handle;
    dl_log_print(ILOG_DEBUG, "pktlib_register protocol %d interface %s vlan %d external_polling %d rx_handler %p returned handle %d ret %d\n", 
        filter_rule.protocol, filter_rule.interface, filter_rule.vlan_id, filter_rule.external_polling, filter_rule.rx_handler, proto.handle, ret);

    pkt_add_proto_info(&proto);
    if (filter_rule.external_polling == 1)
        dl_add_pollfd(proto.handle, POLLIN, external_rx_handler, 0);

    return 0;
}

static int pktlib_set_unregister_cb(cp_context_t *cp, dl_cnx_t *cnx,
                            cp_cb_data_t *data)
{
    const char *interface = {0};
    short idx = 0;
    uint16_t vlan = 0, protocol = 0;
    pktlib_protocol_filter_rule filter_rule;
    int ret = 0;

	memset(&filter_rule, 0, sizeof(filter_rule));
    dl_log_print(ILOG_DEBUG, "pktlib_set_unregister_cb");

    if (NULL != cp_find_attr(cp, data, "interface", &idx)) {
        validate_data_get_char(&data->argt[idx], &interface);
        strncpy(filter_rule.interface, interface, sizeof(filter_rule.interface) - 1);
    }

    if (NULL != cp_find_attr(cp, data, "protocol", &idx)) {
        validate_data_get_uint16(&data->argt[idx], &protocol);
        filter_rule.protocol = protocol;
    }

    if (NULL != cp_find_attr(cp, data, "vlan", &idx)) {
        validate_data_get_uint16(&data->argt[idx], &vlan);
        filter_rule.vlan_id = vlan;
    }

    ret = pktlib_unregister(&filter_rule);
    dl_log_print(ILOG_DEBUG, "pktlib_unregister protocol %d interface %s vlan %d ret %d\n", 
        filter_rule.protocol, filter_rule.interface, filter_rule.vlan_id, ret);

    pkt_del_proto_info(interface, vlan, protocol);

    return 0;
}

static int pktlib_show_protocol_list(cp_context_t *cp, dl_cnx_t *cnx,
                          cp_cb_data_t *data)
{
    test_pktlib_proto_info *current = proto_list_head;

    while (current != NULL) {
        fprintf(cnx->fh, "\tproto %-4d reg_if %-6s reg_vlan_id %-6d reg_external_polling %-1d xmit_dst_if %-6s xmit_src_if %-6s xmit_vlan_num %d xmit_vlan_tpid %x xmit_vlan_id %-6d xmit_vlan_pcp %d handle %d\n",
                        current->reg_proto, current->reg_if, current->reg_vlan_id, current->reg_external_polling, 
                        current->xmit_dst_if, current->xmit_src_if, current->xmit_vlan_num,
                        current->xmit_vlan_tpid, current->xmit_vlan_id, current->xmit_vlan_pcp, current->handle);
        fprintf(cnx->fh, "\n");
        current = current->next;
    }

    return 0;
}

static const cp_cmd_batch_t pktlib_cmd_show[]=
{
    {
        "all-protocol",
        "Show all protocol",
        CP_NO_ATTRS,
        CP_NODE_LEAF,
        CP_NO_CHILD,
        pktlib_show_protocol_list
    },

    CP_CMD_BATCH_END
};

static const cp_cmd_batch_t pktlib_cmd_set[] =
{
    {
        "register",
        "register protocol (1:RSTP 2:G8032 4:ERPS 8:LACP 16:LLDP 32:8021X 64:8021AG 128:PPP 256:ARP 512:DHCPV4 1024:DHCPV6 2048:ICMPV6) "
        "external_polling (0: pktlib poll the socket 1:caller poll the socket)",
        {
            { "interface",        "STRING", CP_ATTR_OPTIONAL },
            { "protocol",         "UINT16",  CP_ATTR_OPTIONAL },
            { "vlan",             "UINT16",  CP_ATTR_OPTIONAL },
            { "external_polling", "UINT16",  CP_ATTR_OPTIONAL },
            { "xmit_src_if",      "STRING", CP_ATTR_OPTIONAL },
            { "xmit_dst_if",      "STRING", CP_ATTR_OPTIONAL },
            { "xmit_vlan_num",    "UINT16",  CP_ATTR_OPTIONAL },
            { "xmit_vlan_tpid",   "UINT16",  CP_ATTR_OPTIONAL },
            { "xmit_vlan_id",     "UINT16",  CP_ATTR_OPTIONAL },
            { "xmit_vlan_pcp",    "UINT16",  CP_ATTR_OPTIONAL },
            CP_ATTR_BATCH_END
        },
        CP_NODE_LEAF,
        CP_NO_CHILD,
        pktlib_set_register_cb
    },
    {
        "unregister",
        "unregister protocol (1:RSTP 2:G8032 4:ERPS 8:LACP 16:LLDP 32:8021X 64:8021AG 128:PPP 256:ARP 512:DHCPV4 1024:DHCPV6 2048:ICMPV6)",
        {
            { "interface", "STRING", CP_ATTR_OPTIONAL },
            { "protocol",  "UINT16",  CP_ATTR_OPTIONAL },
            { "vlan",      "UINT16",  CP_ATTR_OPTIONAL },
            CP_ATTR_BATCH_END
        },
        CP_NODE_LEAF,
        CP_NO_CHILD,
        pktlib_set_unregister_cb
    },

    CP_CMD_BATCH_END
};

cp_cmd_batch_t dm_dcli_root[] = {
    {"set", "Set commands", CP_NO_ATTRS, CP_NODE_CONTAINER, pktlib_cmd_set,
     CP_NO_CALLBACK},
    {"show", "Show commands", CP_NO_ATTRS, CP_NODE_CONTAINER, pktlib_cmd_show,
     CP_NO_CALLBACK},
    CP_CMD_BATCH_END
};

int dm_dcli_init(test_pktlib_ctx_t *root)
{
    /* Initialize Parser */
    root->dcli_cp = cp_new(VALIDATE_TYPES_GLOBAL_PATH);
    if (root->dcli_cp == NULL) {
        return -1;
    }

    /* Add all batch commands to DCLI */
    cp_cmd_add_batch(root->dcli_cp, NULL, dm_dcli_root);
    dl_log_print(ILOG_DEBUG, "%s: dcli commands inserted", __func__);

    return 0;
}

void dm_dcli_handler(char *command, dl_cnx_t *cnx)
{
    cp_cb_data_t             cb_data;

    dl_log_print(ILOG_DEBUG, "%s: received dcli command: <%s>", __func__, command);
    cp_handle_generic_dcli_cmd_cb(p_root_ctx->dcli_cp, cnx, &cb_data, command);
}

static void
dm_dcli_session_close_cb(dl_cnx_t *cnx)
{
    dl_log_print(ILOG_DEBUG, "dcli connection closed");
}

void dm_add_session_ctx(dl_cnx_t *cnx, void *context)
{
    dl_log_print(ILOG_DEBUG, "New dcli connection");
    cnx->user_data = context;
    cnx->disconnect_callback = dm_dcli_session_close_cb;
}

#if 0
static void dm_ipc_handler(void *in_payload, int fd, uint32_t msg_type,
        size_t len, mbox_address_t sma)
{
    return ;
}
#endif

static void do_daemon_init()
{
    /* Set my daemon name
     */
    if (p_root_ctx->myname[0] == 0) {
        strncpy(p_root_ctx->myname, "test_pktlib", DL_PROCESS_NAME_LEN);
    }

    /* The daemonlib has a global context which must be initialized.
     */
    dl_init_daemon_context(p_root_ctx->mydaemon, p_root_ctx->myname);

    /* Set some sane signal handlers for crashing...
     */
    dl_default_signals(dm_sig_handler);

    /* Daemonize us, make sure not to open any files before this.
     */
    if (!(p_root_ctx->flags & _DM_FOREGROUND_)) {
        dl_daemonize_us();
    }

    /* Initialize logging
     */
    if (strlen(p_root_ctx->logfile)) {
        dl_open_logfile(p_root_ctx->logfile);
    }

    dl_log_init(p_root_ctx->mydaemon, p_root_ctx->myname);

    if (!p_root_ctx->mydaemon->logger_mask) {
        p_root_ctx->mydaemon->logger_mask = 0x00FFFFF0;
    }

    dl_log_print(ILOG_INFO, "init logging: first debug log msg: log level %d",
         p_root_ctx->mydaemon->logger_level);

    dl_log_print(ILOG_INFO, "%s Version %d.%d", __func__, VER_MAJOR, VER_MINOR);

    /* Initialize Event library.
     */
    if (dl_event_init(p_root_ctx->mydaemon,
                      p_root_ctx->myname,
                      dm_evt_cb,
                      NULL)) {
        dl_fatal("Failed to initialize the Event library");
    }

    /* Initialize the command parser.
     */
    if (dm_dcli_init(p_root_ctx)) {
        dl_fatal("Failed to initialize the command parser");
    }

    p_root_ctx->schema = sl_load_schema_files();
    if (p_root_ctx->schema == NULL) {
        dl_fatal("Failed to load schema files");
    } else {
        dl_log_print(ILOG_INFO, "Schema files loaded");
    }

#if 0
    dl_log_print(ILOG_INFO, "Calling ipc_init");
    int ret = ipc_init(p_root_ctx->myname, IPC_BUF_SZ, true,
            0, false, dm_ipc_handler);
    if (ret != IPC_OK)
    {
        dl_log_print(ILOG_ERR, "ipc_init failed - err no : %d \n", ret);
    }
#endif

    /* Create my DCLI server socket & handler - bind to localhost.
     */
    p_root_ctx->myserver = dl_dcli_init_server(dm_dcli_handler,
                                           dm_add_session_ctx,
                                           p_root_ctx,
                                           p_root_ctx->myname,
                                           10,
                                           1);

    if (!p_root_ctx->myserver) {
        dl_fatal("Failed to create DCLI server");
    }


    /* Initial our clog (circular log) buffer so we can also trace
     * events that happen during init.
     */
    dl_clog_init(p_root_ctx->mydaemon, 60, 2000, false);
}

void dm_graceful_shutdown(test_pktlib_ctx_t *ipcmgr, uint8_t reboot)
{
    if (p_root_ctx->flags & _DM_DO_RESTART_) {
        dl_log_print(ILOG_INFO, "%s: TEMPLATEMGR graceful restartable shutdown in progress", __func__);
        dl_event_post("shutdown", ETAG_STRING(DL_ATTR_DETAILS, "Graceful restart"));
    } else {
        dl_log_print(ILOG_INFO, "%s: TEMPLATEMGR permanent shutdown in progress", __func__);
        dl_event_post("shutdown", ETAG_STRING(DL_ATTR_DETAILS, "Full stop"));
    }

    /* Close my DCLI server.
     */
    if (p_root_ctx->myserver) {
        dl_dcli_close_server(p_root_ctx->myserver);
    }

    if (p_root_ctx->mydaemon && p_root_ctx->mydaemon->dl_membuff.use_memlog &&
            p_root_ctx->mydaemon->ccb) {
        clog_save_dcli(p_root_ctx->mydaemon->ccb);
    }

    /* And close/free library stuff (event, loggers, pid file etc)
     */
    dl_graceful_shutdown();

    return;
}

void
dm_psm_cb(dl_psm_state state, dl_psm_state old_state, void *ctx)
{
    test_pktlib_ctx_t *root = (test_pktlib_ctx_t *)ctx;

    switch (state) {

    case DL_PSM_STATE_INIT:
        /* Initialize everything
         */
        break;

    case DL_PSM_STATE_WAIT_SB:
        /* Disconnect all clients.
         */
        dl_dcli_disconnect_clients_inrange(root->myserver);
        break;

    case DL_PSM_STATE_UP:
        break;

    case DL_PSM_STATE_SHUTDOWN:
        root->flags |= _DM_DO_SHUTDOWN_;
        break;

    default:
        dl_log_print(ILOG_INFO, "%s: Illegal process state %d!!!",
             __func__, DL_PSM_GET_STATE(root->mydaemon));
        break;
    }
}

int main(int argc, char **argv) {
    int32_t ca;

    memset(p_root_ctx, 0, sizeof(test_pktlib_ctx_t));

    /* Initialize global structures.
     */
    p_root_ctx->mydaemon = dl_new_daemon_context();

    /* TODO Change initial Log Level before delivery.
     */
    p_root_ctx->mydaemon->logger_level = ILOG_INFO;

    /* Read command line options.
     */
    while ((ca = getopt(argc, argv, "hvfso:l:m:n:e:k:r:")) != -1) {
        switch (ca) {
        case 'v':
            exit(0);
            break;
        case 'f':
            p_root_ctx->flags |= 0x1;
            break;
        case 's':
            p_root_ctx->mydaemon->use_syslog = 1;
            break;

        case 'o':
            strncpy(p_root_ctx->logfile, optarg, sizeof(p_root_ctx->logfile) - 1);
            break;

        case 'l':
            p_root_ctx->mydaemon->logger_level = atoi(optarg);
            break;

        case 'm':
            if ((strlen(optarg) > 2) && (optarg[1] == 'x')) {
                sscanf(optarg, "%x", &p_root_ctx->mydaemon->logger_mask);
            } else {
                p_root_ctx->mydaemon->logger_mask = atoi(optarg);
            }
            break;

        case 'n':
            strncpy(p_root_ctx->myname, optarg, strlen(p_root_ctx->myname) - 1);
            break;

        case 'h':
        default:
            usage();
            break;
        }
    }

    /* Doing the first part of initialization
      */
    do_daemon_init();

    dl_log_print(ILOG_INFO, "Initializing SM ");

    /* Initialize process state machine.
     */
    dl_psm_init(p_root_ctx->mydaemon, dm_psm_cb, p_root_ctx);

    while (1) {

        DL_PSM_RUN_SM(p_root_ctx->mydaemon);

        if (p_root_ctx->flags & _DM_DO_SHUTDOWN_) {
            dl_log_print(ILOG_INFO, "%s: _DM_DO_SHUTDOWN_ - exiting", __func__);
            break;
        }

        dl_poll_fds(-1);
        dl_reap_zombie(-1);
    }

    dm_graceful_shutdown(p_root_ctx, 0);

    return 0;
}

