#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <daemonlib.h>
#include <sys/socket.h>
#include <linux/rtnetlink.h>
#include <linux/if_bridge.h>
#include <net/if.h>
#include <pkt_priv.h>

#define NL_BUFSIZE 8192 * 4

pkt_device_info *device_list_head = NULL;
int bridge_vlan_sock = -1;
int vlan_dev_sock = -1;

/* add device info to list */
static void pkt_add_device_info(int vlan_id, int if_index, int master_index, int type)
{
    pkt_device_info *new_device = (pkt_device_info *)malloc(sizeof(pkt_device_info));
    if (new_device == NULL) {
        dl_log_print(ILOG_ERR, "malloc vlan info fail");
        return;
    }

    new_device->vlan_id = vlan_id;
    new_device->if_index = if_index;
    new_device->master_index = master_index;
    new_device->type = type;
    new_device->next = device_list_head;
    device_list_head = new_device;
    dl_log_print(ILOG_DEBUG, "added VLAN ID: %d, Interface Index: %d type %d\n", vlan_id, if_index, type);

    return;
}

/* clear device info list  */
static void pkt_clear_device_info()
{
    pkt_device_info *current = device_list_head;
    pkt_device_info *temp = NULL;

    while (current != NULL) {
        temp = current;
        dl_log_print(ILOG_DEBUG, "Deleted VLAN ID: %d, Interface Index: %d\n", current->vlan_id, current->if_index);
        current = current->next;
        free(temp);
    }
    device_list_head = NULL;
    return;
}

/* print vlan list info */
static void pkt_print_device_list()
{
    char ifname[40] = {0};
    char master_name[40] = {0};
    pkt_device_info *current = device_list_head;

    while (current != NULL) {
        if (current->if_index > 0)
            if_indextoname(current->if_index, ifname);
        if (current->master_index > 0)
            if_indextoname(current->master_index, master_name);
        dl_log_print(ILOG_DEBUG, "print device list VLAN ID: %d, Interface Index: %d %s  master_index %d %s type %d", 
            current->vlan_id, current->if_index, ifname, current->master_index, master_name, current->type);
        current = current->next;
    }
}

/* parse attributes */
static void pkt_parse_rtattr(struct rtattr *tb[], int max, struct rtattr *rta, int len)
{
    while (RTA_OK(rta, len)) {
        if (rta->rta_type <= max)
            tb[rta->rta_type] = rta;
        rta = RTA_NEXT(rta, len);
    }
}

/* parse bridge vlan attributes */
static void pkt_parse_bridge_vlan_info(struct rtattr *af_spec, int ifi_index, int master_ifindex)
{
    struct rtattr *rta = RTA_DATA(af_spec);
    int len = RTA_PAYLOAD(af_spec);

    while (RTA_OK(rta, len)) {
        if (rta->rta_type == IFLA_BRIDGE_VLAN_INFO) {
            struct bridge_vlan_info *vlan_info = RTA_DATA(rta);
            if ((vlan_info->flags & BRIDGE_VLAN_INFO_PVID) == 0 
                    && (vlan_info->flags & BRIDGE_VLAN_INFO_UNTAGGED) == 0
                    && (ifi_index != master_ifindex))
                pkt_add_device_info(vlan_info->vid, ifi_index, master_ifindex, PKT_TYPE_BRIDGE_PORT);
        }
        rta = RTA_NEXT(rta, len);
    }
}

static void pkt_parse_bridge_vlan_link_msg(struct nlmsghdr *nlh)
{
    struct ifinfomsg *ifi = NLMSG_DATA(nlh);
    struct rtattr *tb[IFLA_MAX + 1] = {0};
    struct rtattr *rta = IFLA_RTA(ifi);
    int len = IFLA_PAYLOAD(nlh);

    pkt_parse_rtattr(tb, IFLA_MAX, rta, len);

    if (tb[IFLA_MASTER] && tb[IFLA_IFNAME] && tb[IFLA_AF_SPEC]) {
        int master_ifindex = *(int *)RTA_DATA(tb[IFLA_MASTER]);
        pkt_parse_bridge_vlan_info(tb[IFLA_AF_SPEC], ifi->ifi_index, master_ifindex);
    }

    return;
}

static void pkt_bridge_vlan_device_recv(int nl_sock, int event, void *data)
{
    struct nlmsghdr *nlh;
    char buf[NL_BUFSIZE];
	int len;
	struct iovec iov = { buf, sizeof buf };
	struct sockaddr_nl srcAddr;
	struct msghdr msg;

	if (event & POLLIN ) {
		memset(&msg, 0, sizeof(msg));

		iov.iov_base = buf;
		iov.iov_len = sizeof(buf);
		msg.msg_name = (void*)&srcAddr;
		msg.msg_namelen = sizeof(srcAddr);
		msg.msg_iov = &iov;
		msg.msg_iovlen = 1 ;

		len = recvmsg(nl_sock, &msg, 0);
        if (len < 0) {
            dl_log_print(ILOG_ERR, "receive len  wrong, return");
            return;
        }

        for (nlh = (struct nlmsghdr *)buf; NLMSG_OK(nlh, len); nlh = NLMSG_NEXT(nlh, len)) {
            if (nlh->nlmsg_type == NLMSG_DONE || nlh->nlmsg_type == NLMSG_ERROR) {
                dl_rem_pollfd(nl_sock);
                close(nl_sock);
                bridge_vlan_sock = -1;
                dl_log_print(ILOG_DEBUG, "%s delete get bridge vlan socket %d", __FUNCTION__, nl_sock);
                return;
            }
            pkt_parse_bridge_vlan_link_msg(nlh);
        }
    }

    return;
}

/* get all bridge vlan ports under bridge */
static void pkt_get_all_bridge_vlan_devices()
{
    int sock;
    struct sockaddr_nl sa;
    struct {
        struct nlmsghdr nlh;
        struct ifinfomsg ifi;
        struct rtattr ext_req;
        __u32 ext_mask;
    } req;

    sock = socket(AF_NETLINK, SOCK_RAW, NETLINK_ROUTE);
    if (sock < 0) {
        dl_log_print(ILOG_ERR, "create socket fail");
        return;
    }

    memset(&sa, 0, sizeof(sa));
    sa.nl_family = AF_NETLINK;
    sa.nl_groups = RTMGRP_LINK;

    if (bind(sock, (struct sockaddr *)&sa, sizeof(sa)) < 0) {
        dl_log_print(ILOG_ERR, "bind error");
        close(sock);
        return;
    }

    memset(&req, 0, sizeof(req));
    req.nlh.nlmsg_len = NLMSG_LENGTH(sizeof(struct ifinfomsg) + 
                                    sizeof(struct rtattr) + sizeof(__u32));
    req.nlh.nlmsg_type = RTM_GETLINK;
    req.nlh.nlmsg_flags = NLM_F_REQUEST | NLM_F_DUMP;
    req.nlh.nlmsg_seq = 1;
    req.nlh.nlmsg_pid = getpid();
    req.ifi.ifi_family = AF_BRIDGE;
    req.ext_req.rta_type = IFLA_EXT_MASK;
    req.ext_req.rta_len = RTA_LENGTH(sizeof(__u32));
    req.ext_mask = RTEXT_FILTER_BRVLAN;

    if (send(sock, &req, req.nlh.nlmsg_len, 0) < 0) {
        dl_log_print(ILOG_ERR, "send socket fail");
        close(sock);
        return;
    }

    dl_add_pollfd(sock, POLLIN, pkt_bridge_vlan_device_recv, 0);
    bridge_vlan_sock = sock;
    dl_log_print(ILOG_DEBUG, "%s create get bridge vlan socket %d", __FUNCTION__, sock);
    return;
}

/* get VLAN ID from attributes */
static void pkt_parse_vlan_id(struct rtattr *tb[], int ifi_index)
{
    struct rtattr *linfo[IFLA_INFO_MAX + 1] = {0};
    struct rtattr *rta;
    int len, master_index;
    unsigned short vlan_id;

    master_index = tb[IFLA_LINK] ? *(int *)RTA_DATA(tb[IFLA_LINK]) : -1;

    if (tb[IFLA_LINKINFO]) {
        rta = RTA_DATA(tb[IFLA_LINKINFO]);
        len = RTA_PAYLOAD(tb[IFLA_LINKINFO]);
        pkt_parse_rtattr(linfo, IFLA_INFO_MAX, rta, len);

        if (linfo[IFLA_INFO_KIND] && 
                strcmp(RTA_DATA(linfo[IFLA_INFO_KIND]), "vlan") == 0) {
            if (linfo[IFLA_INFO_DATA]) {
                struct rtattr *vlan_data[IFLA_VLAN_MAX + 1] = {0};
                rta = RTA_DATA(linfo[IFLA_INFO_DATA]);
                len = RTA_PAYLOAD(linfo[IFLA_INFO_DATA]);
                pkt_parse_rtattr(vlan_data, IFLA_VLAN_MAX, rta, len);

                if (vlan_data[IFLA_VLAN_ID]) {
                    vlan_id = *(unsigned short*)RTA_DATA(vlan_data[IFLA_VLAN_ID]);
                    pkt_add_device_info(vlan_id, ifi_index, master_index, PKT_TYPE_VLAN_PORT);
                }
            }
        }
    }

    return;
}

static void pkt_parse_vlan_device_link_msg(struct nlmsghdr *nlh)
{
    struct ifinfomsg *ifi = NLMSG_DATA(nlh);
    struct rtattr *tb[IFLA_MAX + 1] = {0};
    struct rtattr *rta = IFLA_RTA(ifi);
    int attrlen = IFLA_PAYLOAD(nlh);
    int ifi_index = ifi->ifi_index;
    char name[40];

    if (ifi_index > 0) {
        if_indextoname(ifi_index, name);
    }

    pkt_parse_rtattr(tb, IFLA_MAX, rta, attrlen);
    if (tb[IFLA_LINKINFO])
        pkt_parse_vlan_id(tb, ifi_index);

    return;
}

static void pkt_vlan_device_recv(int nl_sock, int event, void *data)
{
    struct nlmsghdr *nlh;
    char buf[NL_BUFSIZE];
	int len;
	struct iovec iov = { buf, sizeof buf };
	struct sockaddr_nl srcAddr;
	struct msghdr msg;

	if (event & POLLIN ) {
		memset(&msg, 0, sizeof(msg));

		iov.iov_base = buf;
		iov.iov_len = sizeof(buf);
		msg.msg_name = (void*)&srcAddr;
		msg.msg_namelen = sizeof(srcAddr);
		msg.msg_iov = &iov;
		msg.msg_iovlen = 1 ;

		len = recvmsg(nl_sock, &msg, 0);
        if (len < 0) {
            dl_log_print(ILOG_ERR, "receive len  wrong, return");
            return;
        }

        for (nlh = (struct nlmsghdr *)buf; NLMSG_OK(nlh, len); nlh = NLMSG_NEXT(nlh, len)) {
            if (nlh->nlmsg_type == NLMSG_DONE || nlh->nlmsg_type == NLMSG_ERROR) {
                dl_rem_pollfd(nl_sock);
                close(nl_sock);
                vlan_dev_sock = -1;
                dl_log_print(ILOG_DEBUG, "%s delete get vlan device socket %d", __FUNCTION__, nl_sock);
                return;
            }
            pkt_parse_vlan_device_link_msg(nlh);
        }
    }

    return;
}

/* get all vlan devices under bridge */
static void pkt_get_all_vlan_devices()
{
    int sock;
    struct {
        struct nlmsghdr hdr;
        struct ifinfomsg ifinfo;
    } req;
    struct sockaddr_nl sa;

    sock = socket(AF_NETLINK, SOCK_RAW, NETLINK_ROUTE);
    if (sock < 0) {
        dl_log_print(ILOG_ERR, "create socket fail");
        return;
    }

    memset(&sa, 0, sizeof(sa));
    sa.nl_family = AF_NETLINK;
    sa.nl_groups = RTMGRP_LINK;
    if (bind(sock, (struct sockaddr *)&sa, sizeof(sa)) < 0) {
        dl_log_print(ILOG_ERR, "bind socket fail");
        close(sock);
        return;
    }

    memset(&req, 0, sizeof(req));
    req.hdr.nlmsg_len = NLMSG_LENGTH(sizeof(struct ifinfomsg));
    req.hdr.nlmsg_type = RTM_GETLINK;
    req.hdr.nlmsg_flags = NLM_F_REQUEST | NLM_F_DUMP;
    req.hdr.nlmsg_seq = 1;
    req.hdr.nlmsg_pid = getpid();
    req.ifinfo.ifi_family = AF_UNSPEC;

    if (send(sock, &req, req.hdr.nlmsg_len, 0) < 0) {
        dl_log_print(ILOG_ERR, "send socket fail");
        close(sock);
        return;
    }

    dl_add_pollfd(sock, POLLIN, pkt_vlan_device_recv, 0);
    vlan_dev_sock = sock;
    dl_log_print(ILOG_DEBUG, "%s create get vlan device socket %d", __FUNCTION__, sock);
	return;
}

static void pkt_notify_nl_recv(int nl_sock, int event, void *data)
{
    struct nlmsghdr *nlh;
    char buf[NL_BUFSIZE];
	int len;
	struct iovec iov = { buf, sizeof buf };
	struct sockaddr_nl srcAddr;
	struct msghdr msg ;

	if (event & POLLIN ) {
        memset(&msg, 0, sizeof(msg));
        iov.iov_base = buf;
        iov.iov_len = sizeof(buf);
        msg.msg_name = (void*)&srcAddr;
        msg.msg_namelen = sizeof(srcAddr);
        msg.msg_iov = &iov;
        msg.msg_iovlen = 1 ;

        len = recvmsg(nl_sock, &msg, 0);
        if (len < 0) {
            dl_log_print(ILOG_ERR, "receive len  wrong, return");
            return;
        }
        nlh = (struct nlmsghdr *)buf;
        for (nlh = (struct nlmsghdr *)buf; NLMSG_OK(nlh, len); nlh = NLMSG_NEXT(nlh, len)) {
            if (nlh->nlmsg_type == RTM_NEWLINK || nlh->nlmsg_type == RTM_DELLINK) {
                if (vlan_dev_sock == -1 && bridge_vlan_sock == -1)
                    pkt_get_all_devices();
                break;
            }
        }
    }

    return;
}

/* subscribe bridge vlan change event */
int pkt_nl_socket_init()
{
    int sockfd;
    struct sockaddr_nl sa;

    sockfd = socket(AF_NETLINK, SOCK_RAW, NETLINK_ROUTE);
    if (sockfd < 0) {
        dl_log_print(ILOG_ERR, "%s Socket creation failed\n", __FUNCTION__);
        return -1;
    }
    memset(&sa, 0, sizeof(sa));
    sa.nl_family = AF_NETLINK;
    sa.nl_groups = RTMGRP_LINK | RTMGRP_NOTIFY;

    if (bind(sockfd, (struct sockaddr *)&sa, sizeof(sa)) < 0) {
        dl_log_print(ILOG_ERR, "%s Socket bind failed\n", __FUNCTION__);
        close(sockfd);
        return -1;
    }

    dl_add_pollfd(sockfd, POLLIN, pkt_notify_nl_recv, 0);
    dl_log_print(ILOG_DEBUG, "%s create net link notify socket %d", __FUNCTION__, sockfd);
    return sockfd;
}

pkt_device_info **pkt_find_device_by_vlan_type(int vlan, int type, int *count)
{
    pkt_device_info *current = device_list_head;
    pkt_device_info **results = NULL;
    *count = 0;

    while (current != NULL) {
        if (current->vlan_id == vlan && current->type == type) {
            results = (pkt_device_info**)realloc(results, (*count + 1) * sizeof(pkt_device_info*));
            results[*count] = current;
            (*count)++;
        }
        current = current->next;
    }

    return results;
}

void pkt_get_all_devices()
{
    /* clear all current devices */
    pkt_clear_device_info();

    /* get all bridge port intfo */
    pkt_get_all_bridge_vlan_devices();

    /* get all vlan devices */
    pkt_get_all_vlan_devices();

    pkt_print_device_list();
}