#############################################################################
#                         _____       _ _                                   # 
#                        /  __ \     | (_)                                  # 
#                        | /  \/ __ _| |___  __                             #
#                        | |    / _` | | \ \/ /                             #
#                        | \__/\ (_| | | |>  <                              #
#                         \____/\__,_|_|_/_/\_\ inc.                        #
#                                                                           #
#############################################################################
#                                                                           #
#                       copyright 2025 by Calix, Inc.                       #
#                             Santa Barbara, CA                             #
#                                                                           #
#############################################################################
#
# Author: <PERSON>
#
# Purpose: Makefile.am for the Daemon Library Template
#
#############################################################################

include_HEADERS = pkt_lib.h

lib_LTLIBRARIES = libpktlib.la

libpktlib_la_LDFLAGS = -version-info @VERSION_INFO@ -lrt -lexa_lib -lnl-3 \
     -lnl-route-3 -lnl-genl-3 -lnl-cli-3 -lcalixdb -lrdtsc -lsystemd -ldaemonlib -lpcap

libpktlib_la_CFLAGS = $(warning_mask) -D_GNU_SOURCE -Werror \
     $(OPTS) -pthread -I${STAGING_DIR_HOST}/usr/include/libnl3

libpktlib_la_SOURCES = pkt_lib.c pkt_device.c pkt_socket.c

if RM_OPT
override CFLAGS := $(shell echo $(CFLAGS) | sed "s@-O.@@g" )
OPTS = -DCODECOV
endif

