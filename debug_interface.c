/*
 * Debug tool to check interface and packet capture
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <errno.h>
#include <sys/socket.h>
#include <linux/if_packet.h>
#include <linux/if_ether.h>
#include <net/if.h>
#include <ifaddrs.h>

void list_interfaces(void)
{
    struct ifaddrs *ifaddrs_ptr, *ifa;
    
    printf("Available network interfaces:\n");
    printf("=============================\n");
    
    if (getifaddrs(&ifaddrs_ptr) == -1) {
        printf("Failed to get interface list: %s\n", strerror(errno));
        return;
    }
    
    for (ifa = ifaddrs_ptr; ifa != NULL; ifa = ifa->ifa_next) {
        if (ifa->ifa_addr && ifa->ifa_addr->sa_family == AF_PACKET) {
            int ifindex = if_nametoindex(ifa->ifa_name);
            printf("  %s (index: %d)\n", ifa->ifa_name, ifindex);
        }
    }
    
    freeifaddrs(ifaddrs_ptr);
    printf("\n");
}

int test_raw_socket(const char *ifname)
{
    int sockfd;
    struct sockaddr_ll sockaddr;
    
    printf("Testing raw socket on interface: %s\n", ifname);
    
    /* Create raw socket */
    sockfd = socket(AF_PACKET, SOCK_RAW, htons(ETH_P_ALL));
    if (sockfd < 0) {
        printf("❌ Failed to create raw socket: %s\n", strerror(errno));
        return -1;
    }
    printf("✅ Raw socket created successfully\n");
    
    /* Get interface index */
    int ifindex = if_nametoindex(ifname);
    if (ifindex == 0) {
        printf("❌ Interface %s not found\n", ifname);
        close(sockfd);
        return -1;
    }
    printf("✅ Interface %s found (index: %d)\n", ifname, ifindex);
    
    /* Bind to interface */
    memset(&sockaddr, 0, sizeof(sockaddr));
    sockaddr.sll_family = AF_PACKET;
    sockaddr.sll_protocol = htons(ETH_P_ALL);
    sockaddr.sll_ifindex = ifindex;
    
    if (bind(sockfd, (struct sockaddr *)&sockaddr, sizeof(sockaddr)) < 0) {
        printf("❌ Failed to bind to %s: %s\n", ifname, strerror(errno));
        close(sockfd);
        return -1;
    }
    printf("✅ Successfully bound to interface %s\n", ifname);
    
    /* Test packet reception */
    printf("Testing packet reception (5 seconds)...\n");
    
    fd_set readfds;
    struct timeval timeout;
    int packet_count = 0;
    
    for (int i = 0; i < 5; i++) {
        FD_ZERO(&readfds);
        FD_SET(sockfd, &readfds);
        timeout.tv_sec = 1;
        timeout.tv_usec = 0;
        
        int result = select(sockfd + 1, &readfds, NULL, NULL, &timeout);
        if (result > 0 && FD_ISSET(sockfd, &readfds)) {
            /* Try to receive a packet */
            char buffer[2048];
            ssize_t len = recv(sockfd, buffer, sizeof(buffer), MSG_DONTWAIT);
            if (len > 0) {
                packet_count++;
                printf("  Received packet %d: %zd bytes\n", packet_count, len);
            }
        }
        printf("  Second %d: %d packets received so far\n", i + 1, packet_count);
    }
    
    if (packet_count > 0) {
        printf("✅ Received %d packets - interface has traffic\n", packet_count);
    } else {
        printf("⚠️  No packets received - interface may have no traffic\n");
        printf("   Try generating traffic: ping -I %s 8.8.8.8\n", ifname);
    }
    
    close(sockfd);
    return packet_count;
}

int main(int argc, char *argv[])
{
    printf("PKTLIB Interface Debug Tool\n");
    printf("===========================\n\n");
    
    /* List all interfaces */
    list_interfaces();
    
    /* Test specific interface */
    const char *test_interface = "eth0";
    if (argc > 1) {
        test_interface = argv[1];
    }
    
    printf("Testing interface: %s\n", test_interface);
    printf("(Use: %s <interface_name> to test different interface)\n\n", argv[0]);
    
    int result = test_raw_socket(test_interface);
    
    printf("\n=== Summary ===\n");
    if (result >= 0) {
        printf("Interface %s is accessible for packet capture\n", test_interface);
        if (result == 0) {
            printf("No traffic detected - this may be normal if interface is idle\n");
            printf("To test with traffic, try:\n");
            printf("  1. ping -I %s 8.8.8.8 (in another terminal)\n", test_interface);
            printf("  2. tcpdump -i %s (to verify traffic exists)\n", test_interface);
        }
    } else {
        printf("Interface %s is not accessible or doesn't exist\n", test_interface);
    }
    
    return 0;
}
