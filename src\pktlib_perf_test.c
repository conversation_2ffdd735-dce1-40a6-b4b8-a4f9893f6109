/*
 * Performance test tool for pktlib
 * This tool can be used to benchmark packet processing performance
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <signal.h>
#include <time.h>
#include <pkt_lib.h>

static volatile int running = 1;
static unsigned long total_packets = 0;
static unsigned long total_bytes = 0;

void signal_handler(int sig)
{
    running = 0;
    printf("\nReceived signal %d, stopping...\n", sig);
}

/* Test packet handler */
static int test_rx_handler(pktlib_protocol_e protocol, 
                          char *interface, int vlan, pktlib_pkt_info *pkt_info)
{
    if (pkt_info) {
        total_packets++;
        total_bytes += pkt_info->data_len;
    }
    return 0;
}

void print_usage(const char *prog_name)
{
    printf("Usage: %s [options]\n", prog_name);
    printf("Options:\n");
    printf("  -i <interface>  Network interface to test (default: eth0)\n");
    printf("  -p <protocol>   Protocol to test (default: 256 for ARP)\n");
    printf("  -v <vlan>       VLAN ID to test (default: 0)\n");
    printf("  -t <seconds>    Test duration in seconds (default: 10)\n");
    printf("  -h              Show this help\n");
    printf("\nProtocol values:\n");
    printf("  1   - RSTP\n");
    printf("  2   - G8032\n");
    printf("  4   - ERPS\n");
    printf("  8   - LACP\n");
    printf("  16  - LLDP\n");
    printf("  32  - 8021X\n");
    printf("  64  - 8021AG\n");
    printf("  128 - PPP\n");
    printf("  256 - ARP\n");
    printf("  512 - DHCPV4\n");
    printf("  1024 - DHCPV6\n");
    printf("  2048 - ICMPV6\n");
}

int main(int argc, char *argv[])
{
    char interface[64] = "eth0";
    int protocol = 256;  /* ARP by default */
    int vlan = 0;
    int test_duration = 10;
    int opt;
    
    /* Parse command line arguments */
    while ((opt = getopt(argc, argv, "i:p:v:t:h")) != -1) {
        switch (opt) {
            case 'i':
                strncpy(interface, optarg, sizeof(interface) - 1);
                break;
            case 'p':
                protocol = atoi(optarg);
                break;
            case 'v':
                vlan = atoi(optarg);
                break;
            case 't':
                test_duration = atoi(optarg);
                break;
            case 'h':
                print_usage(argv[0]);
                return 0;
            default:
                print_usage(argv[0]);
                return 1;
        }
    }
    
    printf("PKTLIB Performance Test\n");
    printf("=======================\n");
    printf("Interface: %s\n", interface);
    printf("Protocol: %d\n", protocol);
    printf("VLAN: %d\n", vlan);
    printf("Duration: %d seconds\n", test_duration);
    printf("\n");
    
    /* Set up signal handlers */
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    
    /* Register protocol with pktlib */
    pktlib_protocol_filter_rule filter_rule = {0};
    filter_rule.protocol = protocol;
    filter_rule.direction = PKTLIB_DIR_INGRESS;
    strncpy(filter_rule.interface, interface, sizeof(filter_rule.interface) - 1);
    filter_rule.vlan_id = vlan;
    filter_rule.external_polling = 0;  /* Use internal polling */
    filter_rule.rx_handler = test_rx_handler;
    
    int ret = pktlib_register(&filter_rule);
    if (ret != 0) {
        printf("Failed to register protocol with pktlib: %d\n", ret);
        return 1;
    }
    
    printf("Protocol registered successfully, handle: %d\n", filter_rule.handle);
    printf("Starting packet capture...\n");
    
    /* Record start time */
    struct timespec start_time, current_time;
    clock_gettime(CLOCK_MONOTONIC, &start_time);
    
    /* Main test loop */
    while (running) {
        sleep(1);
        
        clock_gettime(CLOCK_MONOTONIC, &current_time);
        double elapsed = (current_time.tv_sec - start_time.tv_sec) + 
                        (current_time.tv_nsec - start_time.tv_nsec) / 1e9;
        
        if (elapsed >= test_duration) {
            break;
        }
        
        /* Print periodic statistics */
        double pps, mbps;
        if (pktlib_get_performance(&filter_rule, &pps, &mbps) == 0) {
            printf("Elapsed: %.1fs, PPS: %.2f, Mbps: %.2f, Total: %lu packets, %lu bytes\n",
                   elapsed, pps, mbps, total_packets, total_bytes);
        }
    }
    
    /* Final statistics */
    clock_gettime(CLOCK_MONOTONIC, &current_time);
    double total_time = (current_time.tv_sec - start_time.tv_sec) + 
                       (current_time.tv_nsec - start_time.tv_nsec) / 1e9;
    
    printf("\nTest completed!\n");
    printf("================\n");
    printf("Total time: %.2f seconds\n", total_time);
    printf("Total packets: %lu\n", total_packets);
    printf("Total bytes: %lu\n", total_bytes);
    
    if (total_time > 0) {
        printf("Average PPS: %.2f\n", total_packets / total_time);
        printf("Average Mbps: %.2f\n", (total_bytes * 8.0) / (total_time * 1e6));
    }
    
    /* Print detailed performance statistics */
    printf("\nDetailed Performance Statistics:\n");
    pktlib_print_performance();
    
    /* Cleanup */
    pktlib_unregister(&filter_rule);
    pktlib_cleanup();
    
    return 0;
}
