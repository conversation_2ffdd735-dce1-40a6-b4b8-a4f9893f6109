#!/bin/bash

# Simple compilation test script
echo "Testing pktlib compilation..."

# Try to compile just the library first
echo "Compiling library..."
cd lib
gcc -c -I. -I../lib -DHAVE_CONFIG_H -fPIC -O2 pkt_socket.c -o pkt_socket.o
if [ $? -eq 0 ]; then
    echo "pkt_socket.c compiled successfully"
else
    echo "pkt_socket.c compilation failed"
    exit 1
fi

gcc -c -I. -I../lib -DHAVE_CONFIG_H -fPIC -O2 pkt_lib.c -o pkt_lib.o
if [ $? -eq 0 ]; then
    echo "pkt_lib.c compiled successfully"
else
    echo "pkt_lib.c compilation failed"
    exit 1
fi

gcc -c -I. -I../lib -DHAVE_CONFIG_H -fPIC -O2 pkt_device.c -o pkt_device.o
if [ $? -eq 0 ]; then
    echo "pkt_device.c compiled successfully"
else
    echo "pkt_device.c compilation failed"
    exit 1
fi

echo "All library files compiled successfully!"

# Create shared library
gcc -shared -o libpktlib.so pkt_socket.o pkt_lib.o pkt_device.o -lpcap
if [ $? -eq 0 ]; then
    echo "Shared library created successfully"
else
    echo "Shared library creation failed"
    exit 1
fi

cd ..

# Try to compile test program
echo "Compiling test program..."
cd src
gcc -c -I../lib -DHAVE_CONFIG_H -O2 pktlib_perf_test.c -o pktlib_perf_test.o
if [ $? -eq 0 ]; then
    echo "pktlib_perf_test.c compiled successfully"
else
    echo "pktlib_perf_test.c compilation failed"
    exit 1
fi

# Link test program
gcc -o pktlib_perf_test pktlib_perf_test.o -L../lib -lpktlib -lpcap
if [ $? -eq 0 ]; then
    echo "Test program linked successfully"
else
    echo "Test program linking failed"
    exit 1
fi

echo "All compilation tests passed!"
