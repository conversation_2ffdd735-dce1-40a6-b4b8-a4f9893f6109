#############################################################################
#                         _____       _ _                                   # 
#                        /  __ \     | (_)                                  # 
#                        | /  \/ __ _| |___  __                             #
#                        | |    / _` | | \ \/ /                             #
#                        | \__/\ (_| | | |>  <                              #
#                         \____/\__,_|_|_/_/\_\ inc.                        #
#                                                                           #
#############################################################################
#                                                                           #
#                       copyright 2025 by Calix, Inc.                       #
#                               Petaluma, CA                                #
#                                                                           #
#############################################################################
#
# Author: jane gong
#
# Purpose: Makefile.am for the pktlib sources
#
#############################################################################

bin_PROGRAMS = test_pktlib pktlib_perf_test

test_pktlib_SOURCES = pktlib_main.c

pktlib_perf_test_SOURCES = pktlib_perf_test.c

test_pktlib_CFLAGS = -Werror $(warning_mask) $(SANITIZER_FLAGS) -g $(COV_CCFLAGS_pktlib) -pthread -I$(top_srcdir)

test_pktlib_LDFLAGS = $(SANITIZER_FLAGS) -lrt -lrdtsc -lcalixdb -lexa_lib -ldaemonlib -lcmd_parser -lvalidate -lsl_files
test_pktlib_LDADD = $(top_builddir)/lib/libpktlib.la

pktlib_perf_test_CFLAGS = -Werror $(warning_mask) $(SANITIZER_FLAGS) -g $(COV_CCFLAGS_pktlib) -pthread -I$(top_srcdir)
pktlib_perf_test_LDFLAGS = $(SANITIZER_FLAGS) -lrt -ldaemonlib
pktlib_perf_test_LDADD = $(top_builddir)/lib/libpktlib.la

# Code Coverage Support
if RM_OPT
override CFLAGS := $(shell echo $(CFLAGS) | sed "s@-O.@@g" )
endif
