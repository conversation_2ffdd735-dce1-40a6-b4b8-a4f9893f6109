#ifndef __pkt_priv_h__
#define __pkt_priv_h__

#include <pkt_lib.h>

#define FILTER_LEN       512
#define FILTER_RULE_MAX  128   /* max filter rule for one DM */
#define HEX_BYTES_PER_LINE 16
#define HEX_CHARS_PER_BYTE 3
#define HEX_CHARS_PER_LINE (HEX_BYTES_PER_LINE * HEX_CHARS_PER_BYTE + 1)
typedef struct
{
	pktlib_protocol_filter_rule rule;  /* registed filter rule */
	unsigned long long  rx_packets;
    unsigned long long  tx_packets;
    unsigned long long  tx_fail_packets;
} pkt_proto_info;

typedef struct {
	int initialized;
    int nl_handle;
	pkt_proto_info proto_info[FILTER_RULE_MAX];
} pkt_context;

typedef enum {
	PKT_TYPE_BRIDGE_PORT = 0,
	PKT_TYPE_VLAN_PORT   = 1,
    PKT_TYPE_MAX         = 2
} pkt_port_type_e;

/* save vlan and interface info */
typedef struct pkt_device_info {
    int vlan_id;
    int if_index;
    int master_index;
    pkt_port_type_e type;
    struct pkt_device_info *next;
} pkt_device_info;

int pkt_create_socket(const char *ifname, const char *filter_expr, int external_polling);
pkt_proto_info *pkt_find_proto_info_by_handle(int handle);
int pkt_vlan_push(unsigned char *pkt, unsigned short *data_len,
                            unsigned short tpid, unsigned short tci);
int pkt_send_packet(const int handle, const char *ifname, const unsigned char *pkt,
                            const unsigned short data_len, const unsigned short pri);
int pkt_nl_socket_init();
pkt_device_info **pkt_find_device_by_vlan_type(int vlan, int type, int *count);

void pkt_dump(const unsigned char *data, const unsigned int len);
void pkt_get_all_devices();
#endif	/* __pkt_priv_h__ */
