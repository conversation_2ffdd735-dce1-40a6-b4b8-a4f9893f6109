#ifndef __pkt_priv_h__
#define __pkt_priv_h__

#include <pkt_lib.h>
#include <linux/if_packet.h>

#define FILTER_LEN       512
#define FILTER_RULE_MAX  128   /* max filter rule for one DM */
#define HEX_BYTES_PER_LINE 16
#define HEX_CHARS_PER_BYTE 3
#define HEX_CHARS_PER_LINE (HEX_BYTES_PER_LINE * HEX_CHARS_PER_BYTE + 1)

/* PACKET_MMAP ring buffer configuration */
#define RING_FRAMES      1024    /* Number of frames in ring buffer */
#define FRAME_SIZE       2048    /* Size of each frame (must be power of 2) */
#define BLOCK_SIZE       (FRAME_SIZE * 16)  /* Block size for ring buffer */
#define BLOCK_NR         (RING_FRAMES * FRAME_SIZE / BLOCK_SIZE)  /* Number of blocks */

/* Ring buffer structure for zero-copy packet processing */
typedef struct {
    void *ring_buffer;           /* mmap'd ring buffer */
    struct tpacket_req req;      /* ring buffer configuration */
    unsigned int frame_num;      /* current frame number */
    int sockfd;                  /* associated socket fd */
    int ring_type;               /* RX or TX ring */
} pkt_ring_t;

/* Performance statistics structure */
typedef struct {
    unsigned long long total_packets;      /* Total packets processed */
    unsigned long long total_bytes;        /* Total bytes processed */
    unsigned long long ring_packets;       /* Packets via ring buffer */
    unsigned long long batch_packets;      /* Packets via batch processing */
    unsigned long long single_packets;     /* Packets via single processing */
    unsigned long long dropped_packets;    /* Dropped packets */
    unsigned long long error_packets;      /* Error packets */
    struct timespec start_time;            /* Start time for rate calculation */
    struct timespec last_update;           /* Last statistics update */
    double pps;                            /* Packets per second */
    double mbps;                           /* Megabits per second */
} pkt_perf_stats_t;
typedef struct
{
	pktlib_protocol_filter_rule rule;  /* registed filter rule */
	unsigned long long  rx_packets;
    unsigned long long  tx_packets;
    unsigned long long  tx_fail_packets;
    pkt_ring_t *rx_ring;               /* RX ring buffer for zero-copy */
    pkt_ring_t *tx_ring;               /* TX ring buffer for zero-copy */
    pkt_perf_stats_t perf_stats;       /* Performance statistics */
} pkt_proto_info;

typedef struct {
	int initialized;
    int nl_handle;
	pkt_proto_info proto_info[FILTER_RULE_MAX];
} pkt_context;

typedef enum {
	PKT_TYPE_BRIDGE_PORT = 0,
	PKT_TYPE_VLAN_PORT   = 1,
    PKT_TYPE_MAX         = 2
} pkt_port_type_e;

/* save vlan and interface info */
typedef struct pkt_device_info {
    int vlan_id;
    int if_index;
    int master_index;
    pkt_port_type_e type;
    struct pkt_device_info *next;
} pkt_device_info;

int pkt_create_socket(const char *ifname, const char *filter_expr, int external_polling);
pkt_proto_info *pkt_find_proto_info_by_handle(int handle);
int pkt_vlan_push(unsigned char *pkt, unsigned short *data_len,
                            unsigned short tpid, unsigned short tci);
int pkt_send_packet(const int handle, const char *ifname, const unsigned char *pkt,
                            const unsigned short data_len, const unsigned short pri);
int pkt_nl_socket_init();
pkt_device_info **pkt_find_device_by_vlan_type(int vlan, int type, int *count);

/* PACKET_MMAP ring buffer functions */
pkt_ring_t *pkt_create_rx_ring(int sockfd);
pkt_ring_t *pkt_create_tx_ring(int sockfd);
void pkt_destroy_ring(pkt_ring_t *ring);
int pkt_process_rx_ring(pkt_proto_info *proto_info);
int pkt_send_packet_ring(pkt_ring_t *tx_ring, const char *ifname,
                        const unsigned char *pkt, const unsigned short data_len);

/* Performance monitoring functions */
void pkt_perf_init(pkt_perf_stats_t *stats);
void pkt_perf_update(pkt_perf_stats_t *stats, int packets, int bytes, int method);
void pkt_perf_print(pkt_perf_stats_t *stats, const char *prefix);
double pkt_perf_get_pps(pkt_perf_stats_t *stats);
double pkt_perf_get_mbps(pkt_perf_stats_t *stats);

void pkt_dump(const unsigned char *data, const unsigned int len);
void pkt_get_all_devices();
#endif	/* __pkt_priv_h__ */
