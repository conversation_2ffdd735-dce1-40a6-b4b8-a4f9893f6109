#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <pkt_priv.h>
#include <daemonlib.h>
#include <sys/socket.h>
#include <net/if.h>

pkt_context pkt_ctx = {
	.initialized = 0
};

typedef struct
{
	pktlib_protocol_e proto_code;        /* protocol code */
	char filter_expr[FILTER_LEN];        /* filter expression */
} pkt_proto_filter_expr_t;

static pkt_proto_filter_expr_t pkt_proto_filter_expr[] = 
{
	/* RSTP: Non-IP Packet, DMAC 01:80:c2:00:00:00 */
	{ .proto_code = PKTLIB_PROTO_RSTP,   .filter_expr = {" (ether dst 01:80:c2:00:00:00)"} },
	/* G8032:Non-IP Packet, DMAC 01:19:00:00:00:00~ 01:19:00:00:00:ff and Ethernet Type 0x8902 */
	{ .proto_code = PKTLIB_PROTO_G8032,  .filter_expr = {" (ether proto 0x8902 and (ether[0:4] == 0x01190000 and ether[4:1] == 0x00))"} },
	/* ERPs:Non-IP Packet, DMAC 00:02:5d:1c:8d:19, 00:02:5d:1c:8d:1a, 00:02:5d:1c:8d:22 and Ethernet Type 0x8902*/
	{ .proto_code = PKTLIB_PROTO_ERPS,   .filter_expr = {" (ether proto 0x8902 and (ether dst 00:02:5d:1c:8d:19 or ether dst 00:02:5d:1c:8d:1a or ether dst 00:02:5d:1c:8d:22))"} },
	/* LACP:Non-IP Packet, DMAC 01:80:c2:00:00:02 and Ethernet Type 0x8809 */
	{ .proto_code = PKTLIB_PROTO_LACP,   .filter_expr = {" (ether proto 0x8809 and ether dst 01:80:c2:00:00:02)"} },
	/* LLDP:Non-IP Packet, DMAC 01:80:c2:00:00:0e, 01:80:c2:00:00:00, 01:80:c2:00:00:03 and Ethernet Type 0x88CC */
	{ .proto_code = PKTLIB_PROTO_LLDP,   .filter_expr = {" (ether proto 0x88CC and (ether dst 01:80:c2:00:00:0e or ether dst 01:80:c2:00:00:00 or ether dst 01:80:c2:00:00:03))"} },
	/* 802.1X:DMAC 01:80:c2:00:00:03 and Ethernet Type 0x888e */
	{ .proto_code = PKTLIB_PROTO_8021X,  .filter_expr = {" (ether proto 0x888e and ether dst 01:80:c2:00:00:03)"} },
	/* 802.1ag:DMAC 01:80:c2:00:00:3x or unicast and Ethernet Type 0x8902 */
	{ .proto_code = PKTLIB_PROTO_8021AG, .filter_expr = {" (ether proto 0x8902 and ((ether[0:4] == 0x0180c200 and (ether[4:2] & 0xfff0) == 0x0030) or "
	"(not (ether dst 00:02:5d:1c:8d:19 or ether dst 00:02:5d:1c:8d:1a or ether dst 00:02:5d:1c:8d:22) and not (ether[0:4] == 0x01190000 and ether[4:1] == 0x00))))"} },
	/* PPP/PPPoE:PPP Packet and PPPOE Packet */
	{ .proto_code = PKTLIB_PROTO_PPP,    .filter_expr = {" (ether proto 0x8864 or ether proto 0x8863)"} },
	/* ARP:ARP packet */
	{ .proto_code = PKTLIB_PROTO_ARP,    .filter_expr = {" (arp)"} },
	/* DHCPv4:IPV4 and UDP and DPORT 67/68 */
	{ .proto_code = PKTLIB_PROTO_DHCPV4, .filter_expr = {" (udp port 67 or udp port 68)"} },
	/* DHCPV6:IPv6 and UDP and DPORT 546/547 */
	{ .proto_code = PKTLIB_PROTO_DHCPV6, .filter_expr = {" (ip6 and (udp port 546 or udp port 547))"} },
	/* ICMPV6:ICMPV6 packet */
	{ .proto_code = PKTLIB_PROTO_ICMPV6, .filter_expr = {" (icmp6)"} }
};

static int pkt_init_ctx(pkt_context *ctx)
{
	int ret = 0;

	memset(ctx, 0, sizeof(*ctx));
	for (int i = 0; i < FILTER_RULE_MAX; i++) {
		ctx->proto_info[i].rule.protocol = -1;
		ctx->proto_info[i].rule.handle   = -1;
	}

	return ret;
}

static void pkt_generate_filter_expr(pktlib_protocol_e protocol, int vlan_id, char *filter_expr)
{
	char tmp[FILTER_LEN] = {0};
	char filter_tmp[FILTER_LEN + 16];
	int count = sizeof(pkt_proto_filter_expr) / sizeof(pkt_proto_filter_expr_t);
	int first = 1;

	strcpy(tmp, "(");
	for (int i = 0; i < count; i++) {
		if ((pkt_proto_filter_expr[i].proto_code & protocol) > 0) {
			if (!first)
				strcat(tmp, " or ");
			strcat(tmp, pkt_proto_filter_expr[i].filter_expr);
			first = 0;
		}
	}
	strcat(tmp, ")");

	if (vlan_id > 0) {
		snprintf(filter_tmp, sizeof(filter_tmp), "vlan %d and ", vlan_id);
		int current_len = strlen(filter_tmp);
		int remaining = sizeof(filter_tmp) - current_len - 1;
		snprintf(filter_tmp + current_len, remaining, "%s", tmp);
		memcpy(filter_expr, filter_tmp, sizeof(filter_tmp));
	}
	else
		memcpy(filter_expr, tmp, FILTER_LEN);

	dl_log_print(ILOG_DEBUG, "pkt_generate_filter_expr protocol %d vlan_id %d filter_expr [%s]", protocol, vlan_id, filter_expr);

	return;
}

static pktlib_protocol_filter_rule *pkt_find_filter_rule(pktlib_protocol_filter_rule *filter_rule)
{
	pkt_context *ctx = &pkt_ctx;
	pktlib_protocol_filter_rule *rule = NULL;

	for (int i = 0; i < FILTER_RULE_MAX; i++) {
		rule = &ctx->proto_info[i].rule;
		if (rule->protocol == filter_rule->protocol && rule->vlan_id == filter_rule->vlan_id 
				&& (strncmp(rule->interface, filter_rule->interface, IFNAME_SIZE) == 0)) {
			return rule;
		}
	}

	return NULL;
}

pkt_proto_info *pkt_find_proto_info_by_handle(int handle)
{
	pkt_context *ctx = &pkt_ctx;
	pktlib_protocol_filter_rule *rule = NULL;

	for (int i = 0; i < FILTER_RULE_MAX; i++) {
		rule = &ctx->proto_info[i].rule;
		if (rule->handle == handle) {
			return &ctx->proto_info[i];
		}
	}

	return NULL;
}

static int pkt_add_filter_rule(pktlib_protocol_filter_rule *filter_rule)
{
	int ret = 0;
	int vlan_id, external_polling = 0;
	int socket_fd = -1;
	char ifname[IFNAME_SIZE] = {0};
	/* if vlan_id is set, filter expression need to add vlan filter */
	char filter_expr[FILTER_LEN + 16];
	pktlib_protocol_e protocol;
	pkt_context *ctx = &pkt_ctx;

	protocol = filter_rule->protocol;
	vlan_id = filter_rule->vlan_id;
	strncpy(ifname, filter_rule->interface, IFNAME_SIZE);
	pkt_generate_filter_expr(protocol, vlan_id, filter_expr);

	/* create socket */
	external_polling = filter_rule->external_polling;
	socket_fd = pkt_create_socket(ifname, filter_expr, external_polling);
	if(socket_fd < 0) {
		dl_log_print(ILOG_ERR, "create socket fail, return");
		return -1;
	}

	filter_rule->handle = socket_fd;
	/* save filter rule information */
	for (int i = 0; i < FILTER_RULE_MAX; i++) {
		if (ctx->proto_info[i].rule.handle == -1) {
			ctx->proto_info[i].rx_packets = 0;
			ctx->proto_info[i].tx_packets = 0;
			ctx->proto_info[i].tx_fail_packets = 0;
			ctx->proto_info[i].rx_ring = NULL;
			ctx->proto_info[i].tx_ring = NULL;

			/* Initialize performance statistics */
			pkt_perf_init(&ctx->proto_info[i].perf_stats);
			memcpy(&ctx->proto_info[i].rule, filter_rule, sizeof(pktlib_protocol_filter_rule));

			/* Create PACKET_MMAP ring buffers for high performance */
			ctx->proto_info[i].rx_ring = pkt_create_rx_ring(socket_fd);
			if (!ctx->proto_info[i].rx_ring) {
				dl_log_print(ILOG_INFO, "Failed to create RX ring, using batch processing");
			} else {
				dl_log_print(ILOG_INFO, "RX ring created successfully - zero-copy mode enabled");
			}

			ctx->proto_info[i].tx_ring = pkt_create_tx_ring(socket_fd);
			if (!ctx->proto_info[i].tx_ring) {
				dl_log_print(ILOG_INFO, "TX ring not available, using traditional send");
			}

			dl_log_print(ILOG_DEBUG, "%s index %d proto %d ifname %s vlan_id %d direction %d external_polling %d callback %p handle %d ",
				__FUNCTION__, i, ctx->proto_info[i].rule.protocol, ctx->proto_info[i].rule.interface,
				ctx->proto_info[i].rule.vlan_id, ctx->proto_info[i].rule.direction, ctx->proto_info[i].rule.external_polling,
				ctx->proto_info[i].rule.rx_handler,
				ctx->proto_info[i].rule.handle);
			break;
		}
	}

	return ret;
}

int pktlib_register(pktlib_protocol_filter_rule *filter_rule)
{
	int ret = 0;
	pkt_context *ctx = &pkt_ctx;
	pktlib_protocol_filter_rule *rule = NULL;

	if (filter_rule == NULL) {
		dl_log_print(ILOG_ERR, "%s input filter_rule is NULL", __FUNCTION__);
		return -1;
	}

	dl_log_print(ILOG_DEBUG, "enter %s proto %d ifname %s vlan_id %d direction %d external_polling %d callback %p",
		__FUNCTION__, filter_rule->protocol, filter_rule->interface, filter_rule->vlan_id, filter_rule->direction, filter_rule->external_polling, filter_rule->rx_handler);
	if (!ctx->initialized) {
		pkt_get_all_devices();
		pkt_init_ctx(ctx);
		ctx->nl_handle = pkt_nl_socket_init();
		ctx->initialized = 1;
	}

	rule = pkt_find_filter_rule(filter_rule);
	if (!rule) {
		filter_rule->handle = -1;
		ret = pkt_add_filter_rule(filter_rule);
		if (ret != 0)
			dl_log_print(ILOG_ERR, "register proto fail");
	} else {
		filter_rule->handle = rule->handle;
		/* protocol has already registed, return */
		dl_log_print(ILOG_DEBUG, "the protocol has already registed, created socket is %d", rule->handle);
		return ret;
	}

	return ret;
}

int pktlib_unregister(pktlib_protocol_filter_rule *filter_rule)
{
	int ret = 0;
	pktlib_protocol_filter_rule *rule = NULL;

	if (filter_rule == NULL) {
		dl_log_print(ILOG_ERR, "%s input filter_rule is NULL", __FUNCTION__);
		return -1;
	}

	dl_log_print(ILOG_DEBUG, "%s proto %d ifname %s vlan_id %d",
		__FUNCTION__, filter_rule->protocol, filter_rule->interface, filter_rule->vlan_id);

	rule = pkt_find_filter_rule(filter_rule);
	if (rule) {
		dl_log_print(ILOG_DEBUG, "%s close and remove poll fd %d", __FUNCTION__, rule->handle);
		/* close socket */
		if (rule->handle > 0) {
			dl_rem_pollfd(rule->handle);
			close(rule->handle);
		}

		memset(rule, 0, sizeof(pktlib_protocol_filter_rule));
		rule->protocol = -1;
		rule->handle   = -1;
	} else {
		dl_log_print(ILOG_DEBUG, "can not find the registed protocol");
	}

	return ret;
}

int pktlib_xmit(pktlib_protocol_meta *meta)
{
	int ret = 0, handle, bytes_sent = 0, count;
	unsigned short tci;
	pkt_proto_info *proto_info;
	pkt_device_info **device_infos;

	if (meta == NULL) {
		dl_log_print(ILOG_ERR, "%s input meta is NULL", __FUNCTION__);
		return -1;
	}

	/* Conditional debug logging to reduce overhead in hot path */
	static unsigned long xmit_count = 0;
	if ((++xmit_count % 1000) == 0) {
		dl_log_print(ILOG_DEBUG, "%s handle %d pkt %p pkt_len %d src_interface %s dst_interface %s num_vlan_tags %d", __FUNCTION__,
			meta->handle, meta->pkt_data, meta->pkt_len, meta->src_interface, meta->dst_interface, meta->num_vlan_tags);
		pkt_dump(meta->pkt_data, meta->pkt_len);
	}
	handle = meta->handle;
	proto_info = pkt_find_proto_info_by_handle(handle);
	if (!proto_info) {
		dl_log_print(ILOG_ERR, "%s find protocol info by handle %d failed ", __FUNCTION__, handle);
		return -1;
	}

	/* add inner vlan */
	if (meta->num_vlan_tags == 2 && meta->cvid.vlan != 0) {
		tci = (meta->cvid.pri << 13) | (meta->cvid.cfi << 12) | meta->cvid.vlan;
		ret = pkt_vlan_push(meta->pkt_data, &meta->pkt_len, meta->cvid.tpid, tci);
		if (ret != 0) {
			dl_log_print(ILOG_ERR, "add inner vlan header fail return");
			return ret;
		}
	}

	if ((meta->dst_interface[0] == '\0') && (meta->src_interface[0] == '\0')) {
		/* case 1 flood in the VLAN bridge, no need to add outer vlan header, vlan dev will do it */
		device_infos = pkt_find_device_by_vlan_type(meta->svid.vlan, PKT_TYPE_VLAN_PORT, &count);
		if (count > 0) {
			for (int i = 0; i < count; i++) {
				int if_index = device_infos[i]->if_index;
				char ifname[IFNAME_SIZE];
				if (if_indextoname(if_index, ifname)) {
					/* Use TX ring if available for better performance */
					if (proto_info->tx_ring) {
						bytes_sent = pkt_send_packet_ring(proto_info->tx_ring, ifname, meta->pkt_data, meta->pkt_len);
					} else {
						bytes_sent = pkt_send_packet(handle, ifname, meta->pkt_data, meta->pkt_len, meta->svid.pri);
					}

					if (bytes_sent < 0) {
						dl_log_print(ILOG_ERR, "send packet fail");
						proto_info->tx_fail_packets++;
					} else {
						proto_info->tx_packets++;
					}
				}
			}
			free(device_infos);
		} else {
			dl_log_print(ILOG_ERR, "there is no vlan interface in vlan %d to flood packet", meta->svid.vlan);
		}
	} else {
		/* add outer vlan */
		if (meta->num_vlan_tags > 0 && meta->svid.vlan != 0) {
			tci = (meta->svid.pri << 13) | (meta->svid.cfi << 12) | meta->svid.vlan;
			ret = pkt_vlan_push(meta->pkt_data, &meta->pkt_len, meta->svid.tpid, tci);
            if (ret != 0) {
                dl_log_print(ILOG_ERR, "add outer vlan header fail return");
                return ret;
            }
		}
		/* case 2 specify the destination port to send packet */
		if (meta->dst_interface[0] != '\0') {
			/* Use TX ring if available for better performance */
			if (proto_info->tx_ring) {
				bytes_sent = pkt_send_packet_ring(proto_info->tx_ring, meta->dst_interface, meta->pkt_data, meta->pkt_len);
			} else {
				bytes_sent = pkt_send_packet(handle, meta->dst_interface, meta->pkt_data, meta->pkt_len, meta->svid.pri);
			}

			if (bytes_sent < 0) {
				dl_log_print(ILOG_ERR, "send packet fail");
				proto_info->tx_fail_packets++;
			} else {
				proto_info->tx_packets++;
			}
		}
		/* case 3 flood in the VLAN bridge exclude the source port */
		if (meta->src_interface[0] != '\0') {
			device_infos = pkt_find_device_by_vlan_type(meta->svid.vlan, PKT_TYPE_BRIDGE_PORT, &count);
			if (count > 0) {
				for (int i = 0; i < count; i++) {
					int if_index = device_infos[i]->if_index;
					char ifname[IF_NAMESIZE];
					if (if_indextoname(if_index, ifname) && strncmp(ifname, meta->src_interface, sizeof(ifname)) != 0) {
						/* Use TX ring if available for better performance */
						if (proto_info->tx_ring) {
							bytes_sent = pkt_send_packet_ring(proto_info->tx_ring, ifname, meta->pkt_data, meta->pkt_len);
						} else {
							bytes_sent = pkt_send_packet(handle, ifname, meta->pkt_data, meta->pkt_len, meta->svid.pri);
						}

						if (bytes_sent < 0) {
							dl_log_print(ILOG_ERR, "send packet fail");
							proto_info->tx_fail_packets++;
						} else {
							proto_info->tx_packets++;
						}
					}
				}
				free(device_infos);
			} else {
				dl_log_print(ILOG_ERR, "there is no bridge vlan device in vlan %d", meta->svid.vlan);
			}
		}
	}
	dl_log_print(ILOG_DEBUG, " pktlib_xmit end tx_packets %llu tx_fail_packets %llu", proto_info->tx_packets, proto_info->tx_fail_packets);

	return bytes_sent;
}

/* Clean up function to free ring buffers */
void pktlib_cleanup()
{
    pkt_context *ctx = &pkt_ctx;

    for (int i = 0; i < FILTER_RULE_MAX; i++) {
        if (ctx->proto_info[i].rule.handle != -1) {
            if (ctx->proto_info[i].rx_ring) {
                pkt_destroy_ring(ctx->proto_info[i].rx_ring);
                ctx->proto_info[i].rx_ring = NULL;
            }
            if (ctx->proto_info[i].tx_ring) {
                pkt_destroy_ring(ctx->proto_info[i].tx_ring);
                ctx->proto_info[i].tx_ring = NULL;
            }
        }
    }
}

/* Get performance statistics for a specific protocol */
int pktlib_get_performance(pktlib_protocol_filter_rule *filter_rule, double *pps, double *mbps)
{
    pkt_context *ctx = &pkt_ctx;
    pktlib_protocol_filter_rule *rule;

    if (!filter_rule) {
        return -1;
    }

    rule = pkt_find_filter_rule(filter_rule);
    if (!rule) {
        return -1;
    }

    /* Find the protocol info */
    for (int i = 0; i < FILTER_RULE_MAX; i++) {
        if (ctx->proto_info[i].rule.handle == rule->handle) {
            if (pps) {
                *pps = pkt_perf_get_pps(&ctx->proto_info[i].perf_stats);
            }
            if (mbps) {
                *mbps = pkt_perf_get_mbps(&ctx->proto_info[i].perf_stats);
            }
            return 0;
        }
    }

    return -1;
}

/* Print performance statistics for all registered protocols */
void pktlib_print_performance(void)
{
    pkt_context *ctx = &pkt_ctx;

    dl_log_print(ILOG_INFO, "=== PKTLIB Performance Statistics ===");

    for (int i = 0; i < FILTER_RULE_MAX; i++) {
        if (ctx->proto_info[i].rule.handle != -1) {
            char prefix[256];
            snprintf(prefix, sizeof(prefix), "Protocol %d (Interface: %s, VLAN: %d)",
                    ctx->proto_info[i].rule.protocol,
                    ctx->proto_info[i].rule.interface,
                    ctx->proto_info[i].rule.vlan_id);

            pkt_perf_print(&ctx->proto_info[i].perf_stats, prefix);
            dl_log_print(ILOG_INFO, " ");
        }
    }

    dl_log_print(ILOG_INFO, "=====================================");
}
