# PKTLIB Performance Optimization

## Overview

This document describes the performance optimizations implemented in pktlib to achieve high-throughput packet processing. The optimizations target the bottlenecks that limited performance to ~100Mbps and aim to achieve Gbps-level throughput comparable to iperf.

## Key Performance Optimizations

### 1. PACKET_MMAP Zero-Copy Technology

**Problem**: Traditional `recvmsg()` system calls copy packet data from kernel to user space, creating significant overhead.

**Solution**: Implemented PACKET_RX_RING and PACKET_TX_RING using mmap'd ring buffers.

**Benefits**:
- Zero-copy packet reception and transmission
- Reduced system call overhead
- Batch processing of multiple packets per system call
- Direct memory access to packet data

**Configuration**:
- Ring buffer size: 1024 frames
- Frame size: 2048 bytes
- Block size: 32KB
- Total ring buffer: ~2MB per socket

### 2. Optimized Socket Buffer Configuration

**Problem**: Default socket buffer sizes are too small for high-throughput applications.

**Solution**: Increased socket buffer sizes and added performance-oriented socket options.

**Optimizations**:
- SO_RCVBUF: 16MB receive buffer
- SO_SNDBUF: 16MB send buffer
- SO_TIMESTAMP: Enable packet timestamps
- O_NONBLOCK: Non-blocking socket operations
- TCP_NODELAY: Reduce latency (where applicable)

### 3. Batch Packet Processing

**Problem**: Processing one packet at a time increases per-packet overhead.

**Solution**: Implemented batch processing for both ring buffer and traditional methods.

**Features**:
- Ring buffer: Process up to 32 packets per batch
- Traditional method: Process up to 16 packets per batch
- Reduced system call frequency
- Better CPU cache utilization

### 4. Hot Path Performance Optimization

**Problem**: Debug logging and unnecessary operations in packet processing hot path.

**Solution**: Optimized critical code paths with minimal overhead.

**Optimizations**:
- Conditional debug logging (every 1000th packet)
- Fast packet parser with minimal validation
- Branch prediction hints (likely/unlikely macros)
- Reduced memory allocations
- Optimized VLAN processing

### 5. Performance Monitoring and Statistics

**Problem**: No visibility into performance characteristics and bottlenecks.

**Solution**: Comprehensive performance monitoring system.

**Features**:
- Real-time PPS (Packets Per Second) calculation
- Real-time Mbps (Megabits Per Second) calculation
- Processing method tracking (ring buffer vs batch vs single)
- Dropped packet counting
- Performance API for applications

## Performance Testing

### Using the Performance Test Tool

```bash
# Build the performance test tool
make pktlib_perf_test

# Test ARP packets on eth0 for 30 seconds
./pktlib_perf_test -i eth0 -p 256 -t 30

# Test LLDP packets on eth1 with VLAN 100
./pktlib_perf_test -i eth1 -p 16 -v 100 -t 60
```

### Performance API Usage

```c
#include <pkt_lib.h>

// Get performance statistics
double pps, mbps;
pktlib_protocol_filter_rule filter_rule = {...};
if (pktlib_get_performance(&filter_rule, &pps, &mbps) == 0) {
    printf("Current performance: %.2f PPS, %.2f Mbps\n", pps, mbps);
}

// Print all performance statistics
pktlib_print_performance();
```

## Expected Performance Improvements

### Before Optimization
- Throughput: ~100 Mbps
- Method: Single packet processing with recvmsg()
- Overhead: High system call frequency, memory copies

### After Optimization
- Throughput: 1+ Gbps (target)
- Method: Zero-copy ring buffers with batch processing
- Overhead: Minimal system calls, direct memory access

### Performance Factors

1. **Ring Buffer vs Traditional**: 5-10x improvement
2. **Batch Processing**: 2-3x improvement
3. **Optimized Hot Path**: 20-30% improvement
4. **Socket Buffer Tuning**: 10-20% improvement

## Tuning Guidelines

### System-Level Tuning

```bash
# Increase network buffer sizes
echo 'net.core.rmem_max = 134217728' >> /etc/sysctl.conf
echo 'net.core.wmem_max = 134217728' >> /etc/sysctl.conf
echo 'net.core.rmem_default = 16777216' >> /etc/sysctl.conf
echo 'net.core.wmem_default = 16777216' >> /etc/sysctl.conf

# Increase netdev budget for packet processing
echo 'net.core.netdev_budget = 600' >> /etc/sysctl.conf

# Apply settings
sysctl -p
```

### Application-Level Tuning

1. **Use Ring Buffers**: Automatically enabled for new registrations
2. **Minimize Logging**: Set appropriate log levels in production
3. **CPU Affinity**: Pin application to specific CPU cores
4. **NUMA Awareness**: Ensure memory and network interrupts on same NUMA node

## Monitoring and Debugging

### Performance Metrics

- **PPS**: Packets processed per second
- **Mbps**: Megabits processed per second
- **Ring Buffer Utilization**: Percentage of packets via ring buffer
- **Batch Efficiency**: Average packets per batch
- **Drop Rate**: Percentage of dropped packets

### Debug Information

```c
// Enable detailed performance logging
pktlib_print_performance();

// Check ring buffer status
// Ring buffer creation success/failure logged at ILOG_INFO level
```

## Compatibility

### Fallback Mechanisms

The optimized pktlib maintains backward compatibility:

1. **Ring Buffer Fallback**: If ring buffer creation fails, falls back to batch processing
2. **Batch Processing Fallback**: If batch processing fails, falls back to single packet processing
3. **API Compatibility**: All existing APIs remain unchanged

### Kernel Requirements

- Linux kernel 2.6.27+ for PACKET_MMAP support
- Recommended: Linux 3.0+ for optimal performance

## Troubleshooting

### Common Issues

1. **Ring Buffer Creation Fails**
   - Check available memory
   - Verify kernel PACKET_MMAP support
   - Check user permissions

2. **Performance Not Improved**
   - Verify ring buffers are being used (check logs)
   - Check system-level network tuning
   - Monitor CPU utilization
   - Check for packet drops

3. **Memory Usage Increased**
   - Expected due to larger buffers
   - Monitor with `cat /proc/meminfo`
   - Adjust ring buffer sizes if needed

### Performance Validation

```bash
# Compare with iperf
iperf3 -s &  # Server
iperf3 -c localhost -t 30  # Client

# Test pktlib performance
./pktlib_perf_test -i eth0 -t 30
```

The optimized pktlib should achieve throughput comparable to iperf for packet processing workloads.
