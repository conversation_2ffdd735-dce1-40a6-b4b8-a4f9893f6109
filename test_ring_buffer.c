/*
 * Direct ring buffer test - bypasses pktlib to test PACKET_MMAP directly
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <errno.h>
#include <sys/socket.h>
#include <sys/mman.h>
#include <linux/if_packet.h>
#include <linux/if_ether.h>
#include <net/if.h>
#include <poll.h>

#ifndef TPACKET_ALIGN
#define TPACKET_ALIGN(x) (((x)+TPACKET_ALIGNMENT-1)&~(TPACKET_ALIGNMENT-1))
#endif

#ifndef TPACKET_ALIGNMENT
#define TPACKET_ALIGNMENT 16
#endif

int test_ring_buffer(const char *ifname)
{
    int sockfd;
    struct tpacket_req req;
    void *ring_buffer;
    int page_size = getpagesize();
    
    printf("Testing PACKET_MMAP ring buffer on %s\n", ifname);
    printf("Page size: %d bytes\n", page_size);
    
    /* Create socket */
    sockfd = socket(AF_PACKET, SOCK_RAW, htons(ETH_P_ALL));
    if (sockfd < 0) {
        printf("❌ Failed to create socket: %s\n", strerror(errno));
        return -1;
    }
    
    /* Bind to interface */
    struct sockaddr_ll sockaddr = {0};
    sockaddr.sll_family = AF_PACKET;
    sockaddr.sll_protocol = htons(ETH_P_ALL);
    sockaddr.sll_ifindex = if_nametoindex(ifname);
    
    if (sockaddr.sll_ifindex == 0) {
        printf("❌ Interface %s not found\n", ifname);
        close(sockfd);
        return -1;
    }
    
    if (bind(sockfd, (struct sockaddr *)&sockaddr, sizeof(sockaddr)) < 0) {
        printf("❌ Failed to bind: %s\n", strerror(errno));
        close(sockfd);
        return -1;
    }
    
    /* Configure ring buffer */
    req.tp_block_size = page_size;
    req.tp_frame_size = page_size / 2;
    req.tp_block_nr = 64;
    req.tp_frame_nr = req.tp_block_nr * req.tp_block_size / req.tp_frame_size;
    
    printf("Ring config: block_size=%u, frame_size=%u, block_nr=%u, frame_nr=%u\n",
           req.tp_block_size, req.tp_frame_size, req.tp_block_nr, req.tp_frame_nr);
    
    /* Set up RX ring */
    if (setsockopt(sockfd, SOL_PACKET, PACKET_RX_RING, &req, sizeof(req)) < 0) {
        printf("❌ Failed to setup RX ring: %s\n", strerror(errno));
        close(sockfd);
        return -1;
    }
    printf("✅ RX ring setup successful\n");
    
    /* Map ring buffer */
    size_t ring_size = req.tp_block_size * req.tp_block_nr;
    ring_buffer = mmap(NULL, ring_size, PROT_READ | PROT_WRITE, MAP_SHARED, sockfd, 0);
    if (ring_buffer == MAP_FAILED) {
        printf("❌ Failed to mmap ring: %s\n", strerror(errno));
        close(sockfd);
        return -1;
    }
    printf("✅ Ring buffer mapped: %zu bytes at %p\n", ring_size, ring_buffer);
    
    /* Test packet reception */
    printf("\nTesting packet reception (10 seconds)...\n");
    
    struct pollfd pfd;
    pfd.fd = sockfd;
    pfd.events = POLLIN;
    
    int total_packets = 0;
    unsigned int frame_num = 0;
    
    for (int second = 0; second < 10; second++) {
        int packets_this_second = 0;
        
        /* Poll for 1 second */
        int poll_result = poll(&pfd, 1, 1000);
        
        if (poll_result > 0 && (pfd.revents & POLLIN)) {
            /* Check all frames for packets */
            for (unsigned int i = 0; i < req.tp_frame_nr; i++) {
                struct tpacket_hdr *header = (struct tpacket_hdr *)
                    ((char *)ring_buffer + (frame_num * req.tp_frame_size));
                
                if (header->tp_status & TP_STATUS_USER) {
                    /* Packet available */
                    printf("  Frame %u: packet len=%u, status=0x%x\n", 
                           frame_num, header->tp_len, header->tp_status);
                    
                    /* Mark as processed */
                    header->tp_status = TP_STATUS_KERNEL;
                    
                    packets_this_second++;
                    total_packets++;
                }
                
                frame_num = (frame_num + 1) % req.tp_frame_nr;
            }
        }
        
        printf("Second %d: %d packets received (total: %d)\n", 
               second + 1, packets_this_second, total_packets);
    }
    
    printf("\n=== Results ===\n");
    printf("Total packets received: %d\n", total_packets);
    
    if (total_packets > 0) {
        printf("✅ Ring buffer is working correctly!\n");
    } else {
        printf("⚠️  No packets received through ring buffer\n");
        printf("This could mean:\n");
        printf("1. No traffic on interface %s\n", ifname);
        printf("2. Ring buffer implementation issue\n");
        printf("3. Kernel version compatibility issue\n");
    }
    
    /* Cleanup */
    munmap(ring_buffer, ring_size);
    close(sockfd);
    
    return total_packets;
}

int main(int argc, char *argv[])
{
    const char *interface = "eth0";
    
    if (argc > 1) {
        interface = argv[1];
    }
    
    printf("Direct PACKET_MMAP Ring Buffer Test\n");
    printf("===================================\n\n");
    
    int result = test_ring_buffer(interface);
    
    printf("\n=== Summary ===\n");
    if (result > 0) {
        printf("Ring buffer test PASSED - received %d packets\n", result);
        printf("The issue is likely in pktlib's ring buffer integration\n");
    } else if (result == 0) {
        printf("Ring buffer test completed but no packets received\n");
        printf("Check if there's traffic on %s with: tcpdump -i %s\n", interface, interface);
    } else {
        printf("Ring buffer test FAILED - setup error\n");
    }
    
    return 0;
}
