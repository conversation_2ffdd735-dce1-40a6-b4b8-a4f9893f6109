#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <daemonlib.h>
#include <sys/socket.h>
#include <linux/if_packet.h>
#include <linux/filter.h>
#include <linux/if_ether.h>
#include <net/if.h>
#include <pcap.h>
#include <pkt_priv.h>
#include <arpa/inet.h>
#define VLAN_S 4
#define PROTO_S 2

/* This parses the packet and fills up the info parameter */
static void pkt_parse_packet(pktlib_pkt_info *pkt_info)
{
	pkt_ethHeader_t  *ether_hdr;
    pkt_vlanHeader_t *vlan_hdr;
    int tag_num = 0;
    unsigned char *pkt = pkt_info->pkt;

	pkt_info->ethHeader = ether_hdr = (pkt_ethHeader_t *)pkt;
	memcpy(pkt_info->mac_dest, ether_hdr->mac_dest, sizeof(pkt_info->mac_dest));
	memcpy(pkt_info->mac_source, ether_hdr->mac_source, sizeof(pkt_info->mac_source));

    vlan_hdr = (&pkt_info->ethHeader->vlanHeader - 1);

    while(ntohs(vlan_hdr->etherType) == ETH_P_8021Q || ntohs(vlan_hdr->etherType) == ETH_P_8021AD)
    {
        vlan_hdr++;
        
        if (tag_num == 0) {
            pkt_info->outer_vlan_id = ntohs(vlan_hdr->tci) & 0xFFF;
		    pkt_info->outer_pcp = (ntohs(vlan_hdr->tci) & 0xE000) >> 13;
            dl_log_print(ILOG_DEBUG, "%s outer_vlan_id %d  outer_pcp %d", __FUNCTION__, pkt_info->outer_vlan_id, pkt_info->outer_pcp);
        } else if (tag_num == 1) {
            pkt_info->inner_vlan_id = ntohs(vlan_hdr->tci) & 0xFFF;
		    pkt_info->inner_pcp = (ntohs(vlan_hdr->tci) & 0xE000) >> 13;
            dl_log_print(ILOG_DEBUG, "%s inner_vlan_id %d  inner_pcp %d", __FUNCTION__, pkt_info->inner_vlan_id, pkt_info->inner_pcp);
        }
    
        tag_num++;
    }

    pkt_info->etherType = ntohs(vlan_hdr->etherType);
    dl_log_print(ILOG_DEBUG, "%s etherType 0x%x", __FUNCTION__, pkt_info->etherType);
	if (pkt_info->etherType == ETH_P_IP || pkt_info->etherType == ETH_P_IPV6)
    {
        pkt_info->ipHeader = (pkt_ipHeader_t *)(vlan_hdr + 1);
        struct in_addr addr_src;
        struct in_addr addr_dst;
        char ip_str_src[INET_ADDRSTRLEN];
        char ip_str_dst[INET_ADDRSTRLEN];
        addr_src.s_addr = htonl(pkt_info->ipHeader->ipSrc); 
        addr_dst.s_addr = htonl(pkt_info->ipHeader->ipDest); 
        inet_ntop(AF_INET, &addr_src, ip_str_src, sizeof(ip_str_src));
        inet_ntop(AF_INET, &addr_dst, ip_str_dst, sizeof(ip_str_dst));
        dl_log_print(ILOG_DEBUG, "%s ip_str_src %s ip_str_dst %s", __FUNCTION__, ip_str_src, ip_str_dst);
    }
    else
    {
        pkt_info->non_ip_header = (unsigned char *)(vlan_hdr + 1);
        pkt_info->ipHeader = NULL;
        dl_log_print(ILOG_DEBUG, "%s ipHeader is null", __FUNCTION__);
    }

	pkt_info->igmpHeader = NULL;
	pkt_info->udpHeader = NULL;
    pkt_info->l4Header = NULL;
	if (pkt_info->ipHeader) {
        dl_log_print(ILOG_DEBUG, "%s version %d", __FUNCTION__, pkt_info->ipHeader->version);
		if (pkt_info->ipHeader->version == 4) {
			int	header_len;

			header_len = (pkt_info->ipHeader->ihl) << 2;
 			if (header_len < ntohs(pkt_info->ipHeader->totalLength)) {
                dl_log_print(ILOG_DEBUG, "%s protocol %d", __FUNCTION__, pkt_info->ipHeader->protocol);
				switch (pkt_info->ipHeader->protocol) {
					case IPPROTO_IGMP:
						pkt_info->igmpHeader = (pkt_igmpHeader_t *)((unsigned char *)pkt_info->ipHeader + header_len);
                        dl_log_print(ILOG_DEBUG, "%s protocol is IGMP", __FUNCTION__);
						break;
					case IPPROTO_UDP:
						pkt_info->udpHeader = (pkt_udpHeader_t *)((unsigned char *)pkt_info->ipHeader + header_len);
                        dl_log_print(ILOG_DEBUG, "%s protocol is udpHeader", __FUNCTION__);
						break;
					default:
                        pkt_info->l4Header = (unsigned char *)pkt_info->ipHeader + header_len;
                        dl_log_print(ILOG_DEBUG, "%s protocol is OTHER", __FUNCTION__);
						break;
				}
			}
		} else if (pkt_info->ipHeader->version == 6) {
			switch (((pkt_ipv6Header_t *) pkt_info->ipHeader)->nh) {
				case IPPROTO_UDP:
					pkt_info->udpHeader = (pkt_udpHeader_t *)((unsigned char *)pkt_info->ipHeader + sizeof(pkt_ipv6Header_t));
                    dl_log_print(ILOG_DEBUG, "%s protocol is V6 udpHeader", __FUNCTION__);
					break;
				default:
                    pkt_info->l4Header = (unsigned char *)pkt_info->ipHeader + sizeof(pkt_ipv6Header_t);
                    dl_log_print(ILOG_DEBUG, "%s protocol is V6 OTHER", __FUNCTION__);
					break;
			}
		}
	}

	return;
}

/* get vlan information from msghdr */
static struct tpacket_auxdata *pkt_get_tpacket_vlan(struct msghdr *msgh)
{
    struct cmsghdr *cmsg;

    for (cmsg = CMSG_FIRSTHDR(msgh); cmsg != NULL;
         cmsg = CMSG_NXTHDR(msgh, cmsg)) {
        if (cmsg->cmsg_level == SOL_PACKET &&
            cmsg->cmsg_type == PACKET_AUXDATA) {
            return (struct tpacket_auxdata *)CMSG_DATA(cmsg);
        }
    }
    return NULL;
}

static void bpf_dump_test(const struct bpf_program *p, int option)
{
	const struct bpf_insn *insn;
	int i;
	int n = p->bf_len;

	insn = p->bf_insns;
	if (option > 2) {
		dl_log_print(ILOG_DEBUG, "%d\n", n);
		for (i = 0; i < n; ++insn, ++i) {
			dl_log_print(ILOG_DEBUG, "%u %u %u %u\n", insn->code,
			       insn->jt, insn->jf, insn->k);
		}
		return ;
	}
	if (option > 1) {
        dl_log_print(ILOG_DEBUG, "bf_len %d\n", n);
		for (i = 0; i < n; ++insn, ++i) {
			dl_log_print(ILOG_DEBUG, "{ 0x%x, %d, %d, 0x%08x },",
			       insn->code, insn->jt, insn->jf, insn->k);
        }
		return;
	}
	for (i = 0; i < n; ++insn, ++i) {
#ifdef BDEBUG
		if (i < NBIDS && bids[i] > 0) {
			dl_log_print(ILOG_DEBUG, "[%02d]", bids[i] - 1);
        }
		else {
			dl_log_print(ILOG_DEBUG, " -- ");
        }
#endif
		puts(bpf_image(insn, i));
	}
}

#define HEX_BYTES_PER_LINE 16
#define HEX_CHARS_PER_BYTE 3
#define HEX_CHARS_PER_LINE (HEX_BYTES_PER_LINE * HEX_CHARS_PER_BYTE + 1)
void pkt_dump(const unsigned char *data, const unsigned int len)
{
    int i = 0, bytes = (int)len, stamp = 0;
    char line[HEX_CHARS_PER_LINE], *s;
    dl_log_print(ILOG_DEBUG, "dump %p len %d", data, len);
    s = line;
    while (--bytes >= 0) {
        snprintf(s, HEX_CHARS_PER_BYTE + 1, " %02X", *data++);
        s += HEX_CHARS_PER_BYTE;
        i++;
        if (i >= HEX_BYTES_PER_LINE) {
            dl_log_print(ILOG_DEBUG, "\t0x%04X: %s", stamp, line);
            i = 0;
            s = line;
            stamp += HEX_BYTES_PER_LINE;
        }
    }
    if (i) {
        *s = '\0';
        dl_log_print(ILOG_DEBUG, "\t0x%04X: %s", stamp, line);
    }
}

/* This function adds vlan tag defined by vlan_proto and tci to packet
 * given by msg.
 * In case there is already a vlan tag, this new vlan tag is placed
 * closest to mac addresses
 */
int pkt_vlan_push(unsigned char *pkt, unsigned short *data_len,
                            unsigned short tpid, unsigned short tci)
{
    /* Position to insert new VLAN */
    unsigned char *new_vlan_pos = pkt + sizeof(struct ethhdr) - PROTO_S;

    /* check if there is enough space in packet buffer for new vlan */
    if ((*data_len + VLAN_S) > PKT_LEN_MAX) {
        return -1;
    }

    /* make a 4 byte space for new VLAN tag */
    memmove(new_vlan_pos + VLAN_S, new_vlan_pos, *data_len - sizeof(struct ethhdr) + 2);

    /* update packet length info */
    *data_len += VLAN_S;

    *((unsigned short *)(new_vlan_pos)) = htons((tpid > 0) ? tpid : 0x8100);
    *((unsigned short *)(new_vlan_pos + PROTO_S)) = htons(tci);

    return 0;
}

static void rx_handler(int sockfd, int event, void *data)
{
    char ifname[IFNAME_SIZE];
    struct tpacket_auxdata *tpacket;
	char cmsghdr[CMSG_SPACE(sizeof(*tpacket))];
	int dir, ret;
	pkt_proto_info *proto_info;
    pktlib_pkt_info pkt_info = {0};
	rx_handler_func_t rx_handler;

    if (event & POLLERR) {
        dl_log_print(ILOG_ERR, "%s - Errors in pktlib rx socket. ", __FUNCTION__);
        dl_rem_pollfd(sockfd);
        close(sockfd);    //In case not closed already
        return;
    }

    if (event & POLLIN) {
		proto_info = pkt_find_proto_info_by_handle(sockfd);
		if (!proto_info) {
			dl_log_print(ILOG_ERR, "%s find protocol info by handle %d failed ", __FUNCTION__, sockfd);
			return;
		}
		proto_info->rx_packets++;
		dir = proto_info->rule.direction;
		int size = 0;
        struct sockaddr_ll sockaddr = {0};
        struct msghdr msgh = {
            0,
        };
        struct iovec msg_iov = {
            0,
        };
        unsigned char *msg = pkt_info.pkt;

        msg_iov.iov_base = msg;
        msg_iov.iov_len = PKT_LEN_MAX;

        msgh.msg_name = &sockaddr;
        msgh.msg_namelen = sizeof(sockaddr);
        msgh.msg_iov = &msg_iov;
        msgh.msg_iovlen = 1;
        msgh.msg_control = &cmsghdr;
        msgh.msg_controllen = sizeof(cmsghdr);

        size = recvmsg(sockfd, &msgh, 0);
        if (size == -1) {
            dl_log_print(ILOG_ERR, "failed recv msg from socket");
            return;
        }

        /* do not pass OUTGOING packets to process if DM only need ingress packet */
        if (sockaddr.sll_pkttype == PACKET_OUTGOING && dir == PKTLIB_DIR_INGRESS) {
            dl_log_print(ILOG_DEBUG, "pkttype is PACKET_OUTGOING return");
            return;
        }

        pkt_info.data_len = size;
        pkt_info.ifindex = sockaddr.sll_ifindex;
        if_indextoname(pkt_info.ifindex, ifname);
		memcpy(pkt_info.ifname, ifname, sizeof(pkt_info.ifname));
        tpacket = pkt_get_tpacket_vlan(&msgh);
        if (!tpacket) {
            dl_log_print(ILOG_ERR, "no auxdata found");
            return;
        }

        /* If we have an outer VLAN, move the data 4 bytes to the back and add
         * the VLAN data after the Ethernet header */
        if (tpacket->tp_status & TP_STATUS_VLAN_VALID) {
            ret = pkt_vlan_push(pkt_info.pkt, &pkt_info.data_len, tpacket->tp_vlan_tpid,
                                    tpacket->tp_vlan_tci);
            if (ret != 0) {
                dl_log_print(ILOG_ERR, "add vlan header fail return");
                return;
            }

        }
        dl_log_print(ILOG_DEBUG, "%s handle %d rx_packets %llu ", __FUNCTION__, sockfd, proto_info->rx_packets);
        pkt_dump(pkt_info.pkt, pkt_info.data_len);
		/* parse the packet to build pkt_info */
		pkt_parse_packet(&pkt_info);

        rx_handler = proto_info->rule.rx_handler;
        if (rx_handler)
            rx_handler(proto_info->rule.protocol, proto_info->rule.interface, proto_info->rule.vlan_id, &pkt_info);
    }

    return;
}

static int pkt_apply_fiter(struct sock_fprog prog, const char *ifname)
{
	int sockfd = -1, ret = 0, aux_val = 1;

    sockfd = socket(AF_PACKET, SOCK_RAW, htons(ETH_P_ALL));
    if (sockfd < 0) {
        dl_log_print(ILOG_ERR, "%s Socket creation failed\n", __FUNCTION__);
        return -1;
    }

    /* Request the auxdata with the outer VLAN tag */
    ret = setsockopt(sockfd, SOL_PACKET, PACKET_AUXDATA, &aux_val, sizeof(aux_val));
    if (ret < 0)
    {
        dl_log_print(ILOG_ERR, "failed set socket opt auxdata\n");
        close(sockfd);
        return ret;
    }

	if (ifname != NULL && ifname[0] != '\0') {
		struct sockaddr_ll sockAddr = {};
		sockAddr.sll_family = AF_PACKET;
		sockAddr.sll_protocol = htons(ETH_P_ALL);
		sockAddr.sll_ifindex = if_nametoindex(ifname);
		if (bind(sockfd, (struct sockaddr *)&sockAddr, sizeof(sockAddr)) == -1) {
			dl_log_print(ILOG_ERR, "Failed to bind: %s\n", strerror(errno));
			close(sockfd);
			return -1;
		}
	}

    if (setsockopt(sockfd, SOL_SOCKET, SO_ATTACH_FILTER, &prog, sizeof(prog)) < 0) {
        dl_log_print(ILOG_ERR, "setsockopt(SO_ATTACH_FILTER) failed\n");
        close(sockfd);
        return -1;
    }

    return sockfd;
}

int pkt_create_socket(const char *ifname, const char *filter_expr, int external_polling)
{
    int sockfd = -1;
    struct bpf_program fp;
    char errbuf[PCAP_ERRBUF_SIZE];
    pcap_t *pcap;
    pcap_if_t *alldevs, *dev;

    if (pcap_findalldevs(&alldevs, errbuf) == -1) {
        dl_log_print(ILOG_ERR, "Device scan error: %s\n", errbuf);
        return -1;
    }

    dev = alldevs;
    while (dev && (dev->flags & PCAP_IF_LOOPBACK)) {
        dev = dev->next;
    }

    if (!dev) {
        dl_log_print(ILOG_ERR, "No non-loopback device found\n");
        pcap_freealldevs(alldevs);
        return -1;
    }

    pcap = pcap_open_live(dev->name, 65536, 1, 1000, errbuf);
    if (!pcap) {
        dl_log_print(ILOG_ERR, "Couldn't open device: %s\n", errbuf);
        pcap_freealldevs(alldevs);
        return -1;
    }

    if (pcap_compile(pcap, &fp, filter_expr, 1, PCAP_NETMASK_UNKNOWN) == -1) {
        dl_log_print(ILOG_ERR, "BPF failed %s\n", pcap_geterr(pcap));
        pcap_close(pcap);
        pcap_freealldevs(alldevs);
        return -1;
    }

    bpf_dump_test(&fp, 2);

    struct sock_fprog prog = {
        .len = fp.bf_len,
        .filter = (struct sock_filter *)fp.bf_insns
    };

    sockfd = pkt_apply_fiter(prog, ifname);
    if ((sockfd != -1) && (external_polling == 0))
        dl_add_pollfd(sockfd, POLLIN, rx_handler, 0);

    pcap_freecode(&fp);
    pcap_close(pcap);
    pcap_freealldevs(alldevs);
    return sockfd;
}

int pkt_send_packet(const int handle, const char *ifname, const unsigned char *pkt, const unsigned short data_len, const unsigned short priority)
{
    struct sockaddr_ll socket_address;
    int bytes_sent;

    memset(&socket_address, 0, sizeof(socket_address));
    socket_address.sll_family = AF_PACKET;
    socket_address.sll_protocol = htons(ETH_P_ALL);
    socket_address.sll_ifindex = if_nametoindex(ifname);

    dl_log_print(ILOG_DEBUG, "pkt_send_packet handle %d ifname %s sll_ifindex %d pkt %p data_len %d priority %d", 
        handle, ifname, socket_address.sll_ifindex, pkt, data_len, priority);

    setsockopt(handle, SOL_SOCKET, SO_PRIORITY, &priority, sizeof(priority));
    bytes_sent = sendto(handle, (char *)pkt, data_len, 0, (struct sockaddr *)&socket_address, sizeof(socket_address));
    if (bytes_sent < 0) {
        dl_log_print(ILOG_ERR, "send packet fail\n");
    }

    return bytes_sent;
}
