#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <errno.h>
#include <fcntl.h>
#include <time.h>
#include <sys/mman.h>
#include <netinet/tcp.h>
#include <daemonlib.h>
#include <sys/socket.h>
#include <linux/if_packet.h>
#include <linux/filter.h>
#include <linux/if_ether.h>
#include <net/if.h>
#include <pcap.h>
#include <pkt_priv.h>
#include <arpa/inet.h>

/* Ensure MSG_DONTWAIT is defined */
#ifndef MSG_DONTWAIT
#define MSG_DONTWAIT 0x40
#endif
#define VLAN_S 4
#define PROTO_S 2

/* Branch prediction hints for performance optimization */
#ifdef __GNUC__
#define likely(x)       __builtin_expect(!!(x), 1)
#define unlikely(x)     __builtin_expect(!!(x), 0)
#else
#define likely(x)       (x)
#define unlikely(x)     (x)
#endif

/* Optimized packet parser for hot path - minimal logging */
static void pkt_parse_packet_fast(pktlib_pkt_info *pkt_info)
{
    pkt_ethHeader_t  *ether_hdr;
    pkt_vlanHeader_t *vlan_hdr;
    int tag_num = 0;
    unsigned char *pkt = pkt_info->pkt;

    /* Parse Ethernet header - similar to original but without logging */
    pkt_info->ethHeader = ether_hdr = (pkt_ethHeader_t *)pkt;
    memcpy(pkt_info->mac_dest, ether_hdr->mac_dest, sizeof(pkt_info->mac_dest));
    memcpy(pkt_info->mac_source, ether_hdr->mac_source, sizeof(pkt_info->mac_source));

    vlan_hdr = (&pkt_info->ethHeader->vlanHeader - 1);

    /* Fast VLAN parsing - similar to original logic */
    while(ntohs(vlan_hdr->etherType) == ETH_P_8021Q || ntohs(vlan_hdr->etherType) == ETH_P_8021AD)
    {
        vlan_hdr++;

        if (tag_num == 0) {
            pkt_info->outer_vlan_id = ntohs(vlan_hdr->tci) & 0xFFF;
            pkt_info->outer_pcp = (ntohs(vlan_hdr->tci) & 0xE000) >> 13;
        } else if (tag_num == 1) {
            pkt_info->inner_vlan_id = ntohs(vlan_hdr->tci) & 0xFFF;
            pkt_info->inner_pcp = (ntohs(vlan_hdr->tci) & 0xE000) >> 13;
        }

        tag_num++;
    }

    pkt_info->etherType = ntohs(vlan_hdr->etherType);

    /* Fast IP parsing */
    if (likely(pkt_info->etherType == ETH_P_IP || pkt_info->etherType == ETH_P_IPV6))
    {
        pkt_info->ipHeader = (pkt_ipHeader_t *)((unsigned char *)vlan_hdr + PROTO_S);

        int header_len = (pkt_info->ipHeader->ihl) << 2;
        if (header_len < ntohs(pkt_info->ipHeader->totalLength)) {
            switch (pkt_info->ipHeader->protocol) {
                case IPPROTO_IGMP:
                    pkt_info->igmpHeader = (pkt_igmpHeader_t *)((unsigned char *)pkt_info->ipHeader + header_len);
                    break;
                case IPPROTO_UDP:
                    pkt_info->udpHeader = (pkt_udpHeader_t *)((unsigned char *)pkt_info->ipHeader + header_len);
                    break;
                default:
                    pkt_info->l4Header = (unsigned char *)pkt_info->ipHeader + header_len;
                    break;
            }
        }
    }
    else
    {
        /* Non-IP packet */
        pkt_info->non_ip_header = (unsigned char *)vlan_hdr + PROTO_S;
    }
}

/* Original packet parser with full logging - kept for compatibility */
__attribute__((unused)) static void pkt_parse_packet(pktlib_pkt_info *pkt_info)
{
	pkt_ethHeader_t  *ether_hdr;
    pkt_vlanHeader_t *vlan_hdr;
    int tag_num = 0;
    unsigned char *pkt = pkt_info->pkt;

	pkt_info->ethHeader = ether_hdr = (pkt_ethHeader_t *)pkt;
	memcpy(pkt_info->mac_dest, ether_hdr->mac_dest, sizeof(pkt_info->mac_dest));
	memcpy(pkt_info->mac_source, ether_hdr->mac_source, sizeof(pkt_info->mac_source));

    vlan_hdr = (&pkt_info->ethHeader->vlanHeader - 1);

    while(ntohs(vlan_hdr->etherType) == ETH_P_8021Q || ntohs(vlan_hdr->etherType) == ETH_P_8021AD)
    {
        vlan_hdr++;
        
        if (tag_num == 0) {
            pkt_info->outer_vlan_id = ntohs(vlan_hdr->tci) & 0xFFF;
		    pkt_info->outer_pcp = (ntohs(vlan_hdr->tci) & 0xE000) >> 13;
            dl_log_print(ILOG_DEBUG, "%s outer_vlan_id %d  outer_pcp %d", __FUNCTION__, pkt_info->outer_vlan_id, pkt_info->outer_pcp);
        } else if (tag_num == 1) {
            pkt_info->inner_vlan_id = ntohs(vlan_hdr->tci) & 0xFFF;
		    pkt_info->inner_pcp = (ntohs(vlan_hdr->tci) & 0xE000) >> 13;
            dl_log_print(ILOG_DEBUG, "%s inner_vlan_id %d  inner_pcp %d", __FUNCTION__, pkt_info->inner_vlan_id, pkt_info->inner_pcp);
        }
    
        tag_num++;
    }

    pkt_info->etherType = ntohs(vlan_hdr->etherType);
    dl_log_print(ILOG_DEBUG, "%s etherType 0x%x", __FUNCTION__, pkt_info->etherType);
	if (pkt_info->etherType == ETH_P_IP || pkt_info->etherType == ETH_P_IPV6)
    {
        pkt_info->ipHeader = (pkt_ipHeader_t *)(vlan_hdr + 1);
        struct in_addr addr_src;
        struct in_addr addr_dst;
        char ip_str_src[INET_ADDRSTRLEN];
        char ip_str_dst[INET_ADDRSTRLEN];
        addr_src.s_addr = htonl(pkt_info->ipHeader->ipSrc); 
        addr_dst.s_addr = htonl(pkt_info->ipHeader->ipDest); 
        inet_ntop(AF_INET, &addr_src, ip_str_src, sizeof(ip_str_src));
        inet_ntop(AF_INET, &addr_dst, ip_str_dst, sizeof(ip_str_dst));
        dl_log_print(ILOG_DEBUG, "%s ip_str_src %s ip_str_dst %s", __FUNCTION__, ip_str_src, ip_str_dst);
    }
    else
    {
        pkt_info->non_ip_header = (unsigned char *)(vlan_hdr + 1);
        pkt_info->ipHeader = NULL;
        dl_log_print(ILOG_DEBUG, "%s ipHeader is null", __FUNCTION__);
    }

	pkt_info->igmpHeader = NULL;
	pkt_info->udpHeader = NULL;
    pkt_info->l4Header = NULL;
	if (pkt_info->ipHeader) {
        dl_log_print(ILOG_DEBUG, "%s version %d", __FUNCTION__, pkt_info->ipHeader->version);
		if (pkt_info->ipHeader->version == 4) {
			int	header_len;

			header_len = (pkt_info->ipHeader->ihl) << 2;
 			if (header_len < ntohs(pkt_info->ipHeader->totalLength)) {
                dl_log_print(ILOG_DEBUG, "%s protocol %d", __FUNCTION__, pkt_info->ipHeader->protocol);
				switch (pkt_info->ipHeader->protocol) {
					case IPPROTO_IGMP:
						pkt_info->igmpHeader = (pkt_igmpHeader_t *)((unsigned char *)pkt_info->ipHeader + header_len);
                        dl_log_print(ILOG_DEBUG, "%s protocol is IGMP", __FUNCTION__);
						break;
					case IPPROTO_UDP:
						pkt_info->udpHeader = (pkt_udpHeader_t *)((unsigned char *)pkt_info->ipHeader + header_len);
                        dl_log_print(ILOG_DEBUG, "%s protocol is udpHeader", __FUNCTION__);
						break;
					default:
                        pkt_info->l4Header = (unsigned char *)pkt_info->ipHeader + header_len;
                        dl_log_print(ILOG_DEBUG, "%s protocol is OTHER", __FUNCTION__);
						break;
				}
			}
		} else if (pkt_info->ipHeader->version == 6) {
			switch (((pkt_ipv6Header_t *) pkt_info->ipHeader)->nh) {
				case IPPROTO_UDP:
					pkt_info->udpHeader = (pkt_udpHeader_t *)((unsigned char *)pkt_info->ipHeader + sizeof(pkt_ipv6Header_t));
                    dl_log_print(ILOG_DEBUG, "%s protocol is V6 udpHeader", __FUNCTION__);
					break;
				default:
                    pkt_info->l4Header = (unsigned char *)pkt_info->ipHeader + sizeof(pkt_ipv6Header_t);
                    dl_log_print(ILOG_DEBUG, "%s protocol is V6 OTHER", __FUNCTION__);
					break;
			}
		}
	}

	return;
}

/* get vlan information from msghdr */
static struct tpacket_auxdata *pkt_get_tpacket_vlan(struct msghdr *msgh)
{
    struct cmsghdr *cmsg;

    for (cmsg = CMSG_FIRSTHDR(msgh); cmsg != NULL;
         cmsg = CMSG_NXTHDR(msgh, cmsg)) {
        if (cmsg->cmsg_level == SOL_PACKET &&
            cmsg->cmsg_type == PACKET_AUXDATA) {
            return (struct tpacket_auxdata *)CMSG_DATA(cmsg);
        }
    }
    return NULL;
}

static void bpf_dump_test(const struct bpf_program *p, int option)
{
	const struct bpf_insn *insn;
	int i;
	int n = p->bf_len;

	insn = p->bf_insns;
	if (option > 2) {
		dl_log_print(ILOG_DEBUG, "%d\n", n);
		for (i = 0; i < n; ++insn, ++i) {
			dl_log_print(ILOG_DEBUG, "%u %u %u %u\n", insn->code,
			       insn->jt, insn->jf, insn->k);
		}
		return ;
	}
	if (option > 1) {
        dl_log_print(ILOG_DEBUG, "bf_len %d\n", n);
		for (i = 0; i < n; ++insn, ++i) {
			dl_log_print(ILOG_DEBUG, "{ 0x%x, %d, %d, 0x%08x },",
			       insn->code, insn->jt, insn->jf, insn->k);
        }
		return;
	}
	for (i = 0; i < n; ++insn, ++i) {
#ifdef BDEBUG
		if (i < NBIDS && bids[i] > 0) {
			dl_log_print(ILOG_DEBUG, "[%02d]", bids[i] - 1);
        }
		else {
			dl_log_print(ILOG_DEBUG, " -- ");
        }
#endif
		puts(bpf_image(insn, i));
	}
}

#define HEX_BYTES_PER_LINE 16
#define HEX_CHARS_PER_BYTE 3
#define HEX_CHARS_PER_LINE (HEX_BYTES_PER_LINE * HEX_CHARS_PER_BYTE + 1)
void pkt_dump(const unsigned char *data, const unsigned int len)
{
    int i = 0, bytes = (int)len, stamp = 0;
    char line[HEX_CHARS_PER_LINE], *s;
    dl_log_print(ILOG_DEBUG, "dump %p len %d", data, len);
    s = line;
    while (--bytes >= 0) {
        snprintf(s, HEX_CHARS_PER_BYTE + 1, " %02X", *data++);
        s += HEX_CHARS_PER_BYTE;
        i++;
        if (i >= HEX_BYTES_PER_LINE) {
            dl_log_print(ILOG_DEBUG, "\t0x%04X: %s", stamp, line);
            i = 0;
            s = line;
            stamp += HEX_BYTES_PER_LINE;
        }
    }
    if (i) {
        *s = '\0';
        dl_log_print(ILOG_DEBUG, "\t0x%04X: %s", stamp, line);
    }
}

/* This function adds vlan tag defined by vlan_proto and tci to packet
 * given by msg.
 * In case there is already a vlan tag, this new vlan tag is placed
 * closest to mac addresses
 */
int pkt_vlan_push(unsigned char *pkt, unsigned short *data_len,
                            unsigned short tpid, unsigned short tci)
{
    /* Position to insert new VLAN */
    unsigned char *new_vlan_pos = pkt + sizeof(struct ethhdr) - PROTO_S;

    /* check if there is enough space in packet buffer for new vlan */
    if ((*data_len + VLAN_S) > PKT_LEN_MAX) {
        return -1;
    }

    /* make a 4 byte space for new VLAN tag */
    memmove(new_vlan_pos + VLAN_S, new_vlan_pos, *data_len - sizeof(struct ethhdr) + 2);

    /* update packet length info */
    *data_len += VLAN_S;

    *((unsigned short *)(new_vlan_pos)) = htons((tpid > 0) ? tpid : 0x8100);
    *((unsigned short *)(new_vlan_pos + PROTO_S)) = htons(tci);

    return 0;
}

/* Batch packet processing for traditional recvmsg method */
static int rx_handler_batch(int sockfd, pkt_proto_info *proto_info)
{
    int processed = 0;
    int max_batch = 16;  /* Process up to 16 packets per batch */

    for (int i = 0; i < max_batch; i++) {
        char ifname[IFNAME_SIZE];
        struct tpacket_auxdata *tpacket;
        char cmsghdr[CMSG_SPACE(sizeof(*tpacket))];
        int dir, ret;
        pktlib_pkt_info pkt_info = {0};
        rx_handler_func_t rx_handler_func;

        dir = proto_info->rule.direction;
        int size = 0;
        struct sockaddr_ll sockaddr = {0};
        struct msghdr msgh = {0};
        struct iovec msg_iov = {0};
        unsigned char *msg = pkt_info.pkt;

        msg_iov.iov_base = msg;
        msg_iov.iov_len = PKT_LEN_MAX;

        msgh.msg_name = &sockaddr;
        msgh.msg_namelen = sizeof(sockaddr);
        msgh.msg_iov = &msg_iov;
        msgh.msg_iovlen = 1;
        msgh.msg_control = &cmsghdr;
        msgh.msg_controllen = sizeof(cmsghdr);

        /* Non-blocking receive */
        size = recvmsg(sockfd, &msgh, MSG_DONTWAIT);
        if (size == -1) {
            if (errno == EAGAIN || errno == EWOULDBLOCK) {
                break;  /* No more packets available */
            }
            return processed;  /* Error occurred */
        }

        /* Skip outgoing packets if only ingress is needed */
        if (unlikely(sockaddr.sll_pkttype == PACKET_OUTGOING && dir == PKTLIB_DIR_INGRESS)) {
            continue;
        }

        pkt_info.data_len = size;
        pkt_info.ifindex = sockaddr.sll_ifindex;
        if_indextoname(pkt_info.ifindex, ifname);
        memcpy(pkt_info.ifname, ifname, sizeof(pkt_info.ifname));

        tpacket = pkt_get_tpacket_vlan(&msgh);
        if (unlikely(!tpacket)) {
            continue;
        }

        /* Process VLAN if present */
        if (tpacket->tp_status & TP_STATUS_VLAN_VALID) {
            ret = pkt_vlan_push(pkt_info.pkt, &pkt_info.data_len, tpacket->tp_vlan_tpid,
                                tpacket->tp_vlan_tci);
            if (unlikely(ret != 0)) {
                continue;
            }
        }

        /* Fast packet parsing */
        pkt_parse_packet_fast(&pkt_info);

        /* Call handler */
        rx_handler_func = proto_info->rule.rx_handler;
        if (likely(rx_handler_func)) {
            rx_handler_func(proto_info->rule.protocol, proto_info->rule.interface,
                           proto_info->rule.vlan_id, &pkt_info);
        }

        proto_info->rx_packets++;
        processed++;

        /* Update performance statistics */
        pkt_perf_update(&proto_info->perf_stats, 1, pkt_info.data_len, 1); /* Batch method */
    }

    return processed;
}

static void rx_handler(int sockfd, int event, void *data)
{
    char ifname[IFNAME_SIZE];
    struct tpacket_auxdata *tpacket;
	char cmsghdr[CMSG_SPACE(sizeof(*tpacket))];
	int dir, ret;
	pkt_proto_info *proto_info;
    pktlib_pkt_info pkt_info = {0};
	rx_handler_func_t rx_handler;

    if (event & POLLERR) {
        dl_log_print(ILOG_ERR, "%s - Errors in pktlib rx socket. ", __FUNCTION__);
        dl_rem_pollfd(sockfd);
        close(sockfd);    //In case not closed already
        return;
    }

    if (event & POLLIN) {
		proto_info = pkt_find_proto_info_by_handle(sockfd);
		if (!proto_info) {
			dl_log_print(ILOG_ERR, "%s find protocol info by handle %d failed ", __FUNCTION__, sockfd);
			return;
		}
		proto_info->rx_packets++;
		dir = proto_info->rule.direction;
		int size = 0;
        struct sockaddr_ll sockaddr = {0};
        struct msghdr msgh = {
            0,
        };
        struct iovec msg_iov = {
            0,
        };
        unsigned char *msg = pkt_info.pkt;

        msg_iov.iov_base = msg;
        msg_iov.iov_len = PKT_LEN_MAX;

        msgh.msg_name = &sockaddr;
        msgh.msg_namelen = sizeof(sockaddr);
        msgh.msg_iov = &msg_iov;
        msgh.msg_iovlen = 1;
        msgh.msg_control = &cmsghdr;
        msgh.msg_controllen = sizeof(cmsghdr);

        size = recvmsg(sockfd, &msgh, 0);
        if (size == -1) {
            dl_log_print(ILOG_ERR, "failed recv msg from socket");
            return;
        }

        /* do not pass OUTGOING packets to process if DM only need ingress packet */
        if (unlikely(sockaddr.sll_pkttype == PACKET_OUTGOING && dir == PKTLIB_DIR_INGRESS)) {
            return;  /* Skip debug log in hot path */
        }

        pkt_info.data_len = size;
        pkt_info.ifindex = sockaddr.sll_ifindex;
        if_indextoname(pkt_info.ifindex, ifname);
		memcpy(pkt_info.ifname, ifname, sizeof(pkt_info.ifname));
        tpacket = pkt_get_tpacket_vlan(&msgh);
        if (unlikely(!tpacket)) {
            return;  /* Skip error log in hot path */
        }

        /* If we have an outer VLAN, move the data 4 bytes to the back and add
         * the VLAN data after the Ethernet header */
        if (tpacket->tp_status & TP_STATUS_VLAN_VALID) {
            ret = pkt_vlan_push(pkt_info.pkt, &pkt_info.data_len, tpacket->tp_vlan_tpid,
                                    tpacket->tp_vlan_tci);
            if (unlikely(ret != 0)) {
                return;  /* Skip error log in hot path */
            }
        }

        /* Conditional logging - only log every 1000th packet to reduce overhead */
        if (unlikely((proto_info->rx_packets % 1000) == 0)) {
            dl_log_print(ILOG_DEBUG, "%s handle %d rx_packets %llu ", __FUNCTION__, sockfd, proto_info->rx_packets);
            pkt_dump(pkt_info.pkt, pkt_info.data_len);
        }

		/* Use fast packet parser in hot path */
		pkt_parse_packet_fast(&pkt_info);

        rx_handler = proto_info->rule.rx_handler;
        if (likely(rx_handler))
            rx_handler(proto_info->rule.protocol, proto_info->rule.interface, proto_info->rule.vlan_id, &pkt_info);
    }

    return;
}

/* High-performance RX handler using PACKET_MMAP ring buffers */
static void rx_handler_mmap(int sockfd, int event, void *data)
{
    pkt_proto_info *proto_info;

    if (event & POLLERR) {
        dl_log_print(ILOG_ERR, "%s - Errors in pktlib rx socket. ", __FUNCTION__);
        dl_rem_pollfd(sockfd);
        close(sockfd);
        return;
    }

    if (event & POLLIN) {
        proto_info = pkt_find_proto_info_by_handle(sockfd);
        if (!proto_info) {
            dl_log_print(ILOG_ERR, "%s find protocol info by handle %d failed ", __FUNCTION__, sockfd);
            return;
        }

        dl_log_print(ILOG_DEBUG, "rx_handler_mmap: POLLIN event, has_ring=%s",
                     proto_info->rx_ring ? "yes" : "no");

        /* Use ring buffer if available, otherwise use batch processing */
        if (proto_info->rx_ring) {
            int processed = pkt_process_rx_ring(proto_info);
            dl_log_print(ILOG_DEBUG, "rx_handler_mmap: ring processed %d packets", processed);
            if (processed < 0) {
                dl_log_print(ILOG_ERR, "Failed to process RX ring");
                /* Fall back to batch processing */
                rx_handler_batch(sockfd, proto_info);
            }
        } else {
            /* Use batch processing for better performance */
            int processed = rx_handler_batch(sockfd, proto_info);
            dl_log_print(ILOG_DEBUG, "rx_handler_mmap: batch processed %d packets", processed);
            if (processed == 0) {
                /* Fall back to single packet processing if needed */
                rx_handler(sockfd, event, data);
            }
        }
    }
}

static int pkt_apply_fiter(struct sock_fprog prog, const char *ifname)
{
	int sockfd = -1, ret = 0, aux_val = 1;
    int rcvbuf_size = 16 * 1024 * 1024;  /* 16MB receive buffer */
    int sndbuf_size = 16 * 1024 * 1024;  /* 16MB send buffer */

    sockfd = socket(AF_PACKET, SOCK_RAW, htons(ETH_P_ALL));
    if (sockfd < 0) {
        dl_log_print(ILOG_ERR, "%s Socket creation failed\n", __FUNCTION__);
        return -1;
    }

    /* Optimize socket buffer sizes for high throughput */
    ret = setsockopt(sockfd, SOL_SOCKET, SO_RCVBUF, &rcvbuf_size, sizeof(rcvbuf_size));
    if (ret < 0) {
        dl_log_print(ILOG_ERR, "failed to set SO_RCVBUF: %s\n", strerror(errno));
    }

    ret = setsockopt(sockfd, SOL_SOCKET, SO_SNDBUF, &sndbuf_size, sizeof(sndbuf_size));
    if (ret < 0) {
        dl_log_print(ILOG_ERR, "failed to set SO_SNDBUF: %s\n", strerror(errno));
    }

    /* Enable timestamp reception for better packet processing */
    int timestamp = 1;
    ret = setsockopt(sockfd, SOL_SOCKET, SO_TIMESTAMP, &timestamp, sizeof(timestamp));
    if (ret < 0) {
        dl_log_print(ILOG_ERR, "failed to set SO_TIMESTAMP: %s\n", strerror(errno));
    }

    /* Set socket to non-blocking mode for better performance */
    int flags = fcntl(sockfd, F_GETFL, 0);
    if (flags >= 0) {
        flags |= O_NONBLOCK;
        ret = fcntl(sockfd, F_SETFL, flags);
        if (ret < 0) {
            dl_log_print(ILOG_ERR, "failed to set O_NONBLOCK: %s\n", strerror(errno));
        }
    }

    /* Optimize for low latency */
    int nodelay = 1;
    ret = setsockopt(sockfd, IPPROTO_TCP, TCP_NODELAY, &nodelay, sizeof(nodelay));
    /* Note: TCP_NODELAY may not apply to raw sockets, but doesn't hurt to try */

    /* Request the auxdata with the outer VLAN tag */
    ret = setsockopt(sockfd, SOL_PACKET, PACKET_AUXDATA, &aux_val, sizeof(aux_val));
    if (ret < 0)
    {
        dl_log_print(ILOG_ERR, "failed set socket opt auxdata\n");
        close(sockfd);
        return ret;
    }

	if (ifname != NULL && ifname[0] != '\0') {
		struct sockaddr_ll sockAddr = {};
		sockAddr.sll_family = AF_PACKET;
		sockAddr.sll_protocol = htons(ETH_P_ALL);
		sockAddr.sll_ifindex = if_nametoindex(ifname);

		dl_log_print(ILOG_DEBUG, "Binding socket to interface %s (index %d)", ifname, sockAddr.sll_ifindex);

		if (sockAddr.sll_ifindex == 0) {
			dl_log_print(ILOG_ERR, "Interface %s not found", ifname);
			close(sockfd);
			return -1;
		}

		if (bind(sockfd, (struct sockaddr *)&sockAddr, sizeof(sockAddr)) == -1) {
			dl_log_print(ILOG_ERR, "Failed to bind to %s: %s", ifname, strerror(errno));
			close(sockfd);
			return -1;
		}

		dl_log_print(ILOG_DEBUG, "Successfully bound socket to %s", ifname);
	}

    if (setsockopt(sockfd, SOL_SOCKET, SO_ATTACH_FILTER, &prog, sizeof(prog)) < 0) {
        dl_log_print(ILOG_ERR, "setsockopt(SO_ATTACH_FILTER) failed\n");
        close(sockfd);
        return -1;
    }

    return sockfd;
}

int pkt_create_socket(const char *ifname, const char *filter_expr, int external_polling)
{
    int sockfd = -1;
    struct bpf_program fp;
    char errbuf[PCAP_ERRBUF_SIZE];
    pcap_t *pcap;
    pcap_if_t *alldevs, *dev;

    if (pcap_findalldevs(&alldevs, errbuf) == -1) {
        dl_log_print(ILOG_ERR, "Device scan error: %s\n", errbuf);
        return -1;
    }

    dev = alldevs;
    while (dev && (dev->flags & PCAP_IF_LOOPBACK)) {
        dev = dev->next;
    }

    if (!dev) {
        dl_log_print(ILOG_ERR, "No non-loopback device found\n");
        pcap_freealldevs(alldevs);
        return -1;
    }

    pcap = pcap_open_live(dev->name, 65536, 1, 1000, errbuf);
    if (!pcap) {
        dl_log_print(ILOG_ERR, "Couldn't open device: %s\n", errbuf);
        pcap_freealldevs(alldevs);
        return -1;
    }

    if (pcap_compile(pcap, &fp, filter_expr, 1, PCAP_NETMASK_UNKNOWN) == -1) {
        dl_log_print(ILOG_ERR, "BPF failed %s\n", pcap_geterr(pcap));
        pcap_close(pcap);
        pcap_freealldevs(alldevs);
        return -1;
    }

    bpf_dump_test(&fp, 2);

    struct sock_fprog prog = {
        .len = fp.bf_len,
        .filter = (struct sock_filter *)fp.bf_insns
    };

    sockfd = pkt_apply_fiter(prog, ifname);
    if ((sockfd != -1) && (external_polling == 0))
        dl_add_pollfd(sockfd, POLLIN, rx_handler_mmap, 0);

    pcap_freecode(&fp);
    pcap_close(pcap);
    pcap_freealldevs(alldevs);
    return sockfd;
}

int pkt_send_packet(const int handle, const char *ifname, const unsigned char *pkt, const unsigned short data_len, const unsigned short priority)
{
    struct sockaddr_ll socket_address;
    int bytes_sent;

    memset(&socket_address, 0, sizeof(socket_address));
    socket_address.sll_family = AF_PACKET;
    socket_address.sll_protocol = htons(ETH_P_ALL);
    socket_address.sll_ifindex = if_nametoindex(ifname);

    /* Conditional debug logging to reduce overhead */
    static unsigned long send_count = 0;
    if (unlikely((++send_count % 1000) == 0)) {
        dl_log_print(ILOG_DEBUG, "pkt_send_packet handle %d ifname %s sll_ifindex %d pkt %p data_len %d priority %d",
            handle, ifname, socket_address.sll_ifindex, pkt, data_len, priority);
    }

    setsockopt(handle, SOL_SOCKET, SO_PRIORITY, &priority, sizeof(priority));
    bytes_sent = sendto(handle, (char *)pkt, data_len, 0, (struct sockaddr *)&socket_address, sizeof(socket_address));
    if (bytes_sent < 0) {
        dl_log_print(ILOG_ERR, "send packet fail\n");
    }

    return bytes_sent;
}

/* Create RX ring buffer for zero-copy packet reception */
pkt_ring_t *pkt_create_rx_ring(int sockfd)
{
    pkt_ring_t *ring = malloc(sizeof(pkt_ring_t));
    if (!ring) {
        dl_log_print(ILOG_ERR, "Failed to allocate ring structure");
        return NULL;
    }

    memset(ring, 0, sizeof(pkt_ring_t));
    ring->sockfd = sockfd;
    ring->ring_type = PACKET_RX_RING;

    /* Configure ring buffer parameters - use conservative values */
    int page_size = getpagesize();
    ring->req.tp_block_size = page_size;      /* Use page size for block */
    ring->req.tp_frame_size = page_size / 2;  /* Half page per frame */
    ring->req.tp_block_nr = 64;               /* 64 blocks = 256KB total */
    ring->req.tp_frame_nr = ring->req.tp_block_nr * ring->req.tp_block_size / ring->req.tp_frame_size;

    /* Validate parameters */
    if (ring->req.tp_block_size % page_size != 0) {
        dl_log_print(ILOG_ERR, "Block size must be multiple of page size (%d)", page_size);
        free(ring);
        return NULL;
    }

    if (ring->req.tp_frame_size < 128) {  /* Minimum reasonable frame size */
        dl_log_print(ILOG_ERR, "Frame size too small: %u", ring->req.tp_frame_size);
        free(ring);
        return NULL;
    }

    dl_log_print(ILOG_DEBUG, "Ring params: block_size=%u, frame_size=%u, block_nr=%u, frame_nr=%u",
                 ring->req.tp_block_size, ring->req.tp_frame_size,
                 ring->req.tp_block_nr, ring->req.tp_frame_nr);

    /* Set up RX ring */
    if (setsockopt(sockfd, SOL_PACKET, PACKET_RX_RING, &ring->req, sizeof(ring->req)) < 0) {
        dl_log_print(ILOG_ERR, "Failed to setup RX ring: %s (block_size=%u, frame_size=%u, block_nr=%u, frame_nr=%u)",
                     strerror(errno), ring->req.tp_block_size, ring->req.tp_frame_size,
                     ring->req.tp_block_nr, ring->req.tp_frame_nr);
        free(ring);
        return NULL;
    }

    /* Map ring buffer to memory */
    size_t ring_size = ring->req.tp_block_size * ring->req.tp_block_nr;
    ring->ring_buffer = mmap(NULL, ring_size, PROT_READ | PROT_WRITE, MAP_SHARED, sockfd, 0);
    if (ring->ring_buffer == MAP_FAILED) {
        dl_log_print(ILOG_ERR, "Failed to mmap RX ring: %s", strerror(errno));
        /* Clean up the ring setup */
        struct tpacket_req req_zero = {0};
        setsockopt(sockfd, SOL_PACKET, PACKET_RX_RING, &req_zero, sizeof(req_zero));
        free(ring);
        return NULL;
    }

    dl_log_print(ILOG_INFO, "Created RX ring: %d frames, %d bytes per frame, %zu total bytes",
                 ring->req.tp_frame_nr, ring->req.tp_frame_size, ring_size);

    return ring;
}

/* Create TX ring buffer for zero-copy packet transmission */
pkt_ring_t *pkt_create_tx_ring(int sockfd)
{
    /* Note: For simplicity, we'll disable TX ring for now and focus on RX optimization */
    /* TX ring requires more complex setup with both RX and TX rings in same mmap */
    dl_log_print(ILOG_INFO, "TX ring creation skipped - using traditional send method");
    return NULL;

    /* TODO: Implement proper TX ring setup
    pkt_ring_t *ring = malloc(sizeof(pkt_ring_t));
    if (!ring) {
        dl_log_print(ILOG_ERR, "Failed to allocate TX ring structure");
        return NULL;
    }

    memset(ring, 0, sizeof(pkt_ring_t));
    ring->sockfd = sockfd;
    ring->ring_type = PACKET_TX_RING;

    // Configure ring buffer parameters
    ring->req.tp_block_size = BLOCK_SIZE;
    ring->req.tp_frame_size = FRAME_SIZE;
    ring->req.tp_block_nr = BLOCK_NR;
    ring->req.tp_frame_nr = RING_FRAMES;

    // Set up TX ring
    if (setsockopt(sockfd, SOL_PACKET, PACKET_TX_RING, &ring->req, sizeof(ring->req)) < 0) {
        dl_log_print(ILOG_ERR, "Failed to setup TX ring: %s", strerror(errno));
        free(ring);
        return NULL;
    }

    // Map ring buffer to memory
    size_t ring_size = ring->req.tp_block_size * ring->req.tp_block_nr;
    ring->ring_buffer = mmap(NULL, ring_size, PROT_READ | PROT_WRITE, MAP_SHARED, sockfd, 0);
    if (ring->ring_buffer == MAP_FAILED) {
        dl_log_print(ILOG_ERR, "Failed to mmap TX ring: %s", strerror(errno));
        free(ring);
        return NULL;
    }

    dl_log_print(ILOG_INFO, "Created TX ring: %d frames, %d bytes per frame, %zu total bytes",
                 ring->req.tp_frame_nr, ring->req.tp_frame_size, ring_size);

    return ring;
    */
}

/* Destroy ring buffer and free resources */
void pkt_destroy_ring(pkt_ring_t *ring)
{
    if (!ring) return;

    if (ring->ring_buffer && ring->ring_buffer != MAP_FAILED) {
        size_t ring_size = ring->req.tp_block_size * ring->req.tp_block_nr;
        munmap(ring->ring_buffer, ring_size);
    }

    free(ring);
}

/* High-performance packet processing using RX ring buffer */
int pkt_process_rx_ring(pkt_proto_info *proto_info)
{
    pkt_ring_t *ring = proto_info->rx_ring;
    if (!ring || !ring->ring_buffer) {
        dl_log_print(ILOG_DEBUG, "pkt_process_rx_ring: no ring buffer available");
        return -1;
    }

    int processed = 0;
    unsigned int frame_num = ring->frame_num;

    dl_log_print(ILOG_DEBUG, "pkt_process_rx_ring: checking frame %u", frame_num);

    /* Process multiple packets in batch to reduce overhead */
    for (int batch = 0; batch < 32; batch++) {  /* Process up to 32 packets per call */
        struct tpacket_hdr *header = (struct tpacket_hdr *)
            ((char *)ring->ring_buffer + (frame_num * ring->req.tp_frame_size));

        /* Check if frame is ready */
        if (!(header->tp_status & TP_STATUS_USER)) {
            if (batch == 0) {
                dl_log_print(ILOG_DEBUG, "pkt_process_rx_ring: no packets ready, status=0x%x", header->tp_status);
            }
            break;  /* No more packets available */
        }

        dl_log_print(ILOG_DEBUG, "pkt_process_rx_ring: processing packet %d, status=0x%x", batch, header->tp_status);

        /* Extract packet data */
        unsigned char *pkt_data = (unsigned char *)header + header->tp_mac;
        unsigned short pkt_len = header->tp_len;

        /* Create packet info structure */
        pktlib_pkt_info pkt_info = {0};
        pkt_info.data_len = pkt_len;
        memcpy(pkt_info.pkt, pkt_data, pkt_len);
        /* Get interface info from protocol rule */
        pkt_info.ifindex = if_nametoindex(proto_info->rule.interface);
        strncpy(pkt_info.ifname, proto_info->rule.interface, sizeof(pkt_info.ifname));

        /* Skip outgoing packets if only ingress is needed */
        if (header->tp_status & TP_STATUS_SEND_REQUEST &&
            proto_info->rule.direction == PKTLIB_DIR_INGRESS) {
            goto next_frame;
        }

        /* Process VLAN information if available - optimized for performance */
        if (header->tp_status & TP_STATUS_VLAN_VALID) {
            /* VLAN info is in tp_vlan_tci field for newer kernels */
            /* This would need kernel version specific handling */
        }

        /* Parse packet and call handler - hot path optimized */
        pkt_parse_packet_fast(&pkt_info);  /* Use optimized parser */

        if (likely(proto_info->rule.rx_handler)) {
            proto_info->rule.rx_handler(proto_info->rule.protocol,
                                      proto_info->rule.interface,
                                      proto_info->rule.vlan_id,
                                      &pkt_info);
        }

        proto_info->rx_packets++;
        processed++;

        /* Update performance statistics */
        pkt_perf_update(&proto_info->perf_stats, 1, pkt_len, 0); /* Ring buffer method */

    next_frame:
        /* Mark frame as processed */
        header->tp_status = TP_STATUS_KERNEL;

        /* Move to next frame */
        frame_num = (frame_num + 1) % ring->req.tp_frame_nr;
    }

    ring->frame_num = frame_num;
    return processed;
}

/* High-performance packet sending using TX ring buffer */
int pkt_send_packet_ring(pkt_ring_t *tx_ring, const char *ifname,
                        const unsigned char *pkt, const unsigned short data_len)
{
    if (!tx_ring || !tx_ring->ring_buffer || !pkt || data_len == 0) {
        return -1;
    }

    unsigned int frame_num = tx_ring->frame_num;
    struct tpacket_hdr *header = (struct tpacket_hdr *)
        ((char *)tx_ring->ring_buffer + (frame_num * tx_ring->req.tp_frame_size));

    /* Wait for frame to be available */
    int retries = 1000;
    while ((header->tp_status != TP_STATUS_AVAILABLE) && retries-- > 0) {
        /* Frame not ready, could use poll() here for better efficiency */
        usleep(1);
    }

    if (header->tp_status != TP_STATUS_AVAILABLE) {
        dl_log_print(ILOG_ERR, "TX ring frame not available\n");
        return -1;
    }

    /* Check if packet fits in frame */
    if (data_len > tx_ring->req.tp_frame_size - sizeof(struct tpacket_hdr)) {
        dl_log_print(ILOG_ERR, "Packet too large for TX ring frame\n");
        return -1;
    }

    /* Copy packet data to ring buffer */
    unsigned char *frame_data = (unsigned char *)header + sizeof(struct tpacket_hdr);
    memcpy(frame_data, pkt, data_len);

    /* Set up packet header */
    header->tp_len = data_len;
    header->tp_snaplen = data_len;

    /* Mark frame as ready for transmission */
    header->tp_status = TP_STATUS_SEND_REQUEST;

    /* Move to next frame */
    tx_ring->frame_num = (frame_num + 1) % tx_ring->req.tp_frame_nr;

    /* Trigger transmission */
    if (send(tx_ring->sockfd, NULL, 0, 0) < 0) {
        dl_log_print(ILOG_ERR, "Failed to trigger TX ring transmission\n");
        return -1;
    }

    return data_len;
}

/* Performance monitoring functions */
void pkt_perf_init(pkt_perf_stats_t *stats)
{
    if (!stats) return;

    memset(stats, 0, sizeof(pkt_perf_stats_t));
    clock_gettime(CLOCK_MONOTONIC, &stats->start_time);
    stats->last_update = stats->start_time;
}

void pkt_perf_update(pkt_perf_stats_t *stats, int packets, int bytes, int method)
{
    if (!stats) return;

    stats->total_packets += packets;
    stats->total_bytes += bytes;

    /* Track processing method */
    switch (method) {
        case 0: /* Ring buffer */
            stats->ring_packets += packets;
            break;
        case 1: /* Batch processing */
            stats->batch_packets += packets;
            break;
        case 2: /* Single packet */
            stats->single_packets += packets;
            break;
    }

    /* Update rates every second */
    struct timespec now;
    clock_gettime(CLOCK_MONOTONIC, &now);

    double time_diff = (now.tv_sec - stats->last_update.tv_sec) +
                      (now.tv_nsec - stats->last_update.tv_nsec) / 1e9;

    if (time_diff >= 1.0) {
        double total_time = (now.tv_sec - stats->start_time.tv_sec) +
                           (now.tv_nsec - stats->start_time.tv_nsec) / 1e9;

        if (total_time > 0) {
            stats->pps = stats->total_packets / total_time;
            stats->mbps = (stats->total_bytes * 8.0) / (total_time * 1e6);
        }

        stats->last_update = now;
    }
}

void pkt_perf_print(pkt_perf_stats_t *stats, const char *prefix)
{
    if (!stats || !prefix) return;

    dl_log_print(ILOG_INFO, "%s Performance Stats:", prefix);
    dl_log_print(ILOG_INFO, "  Total Packets: %llu", stats->total_packets);
    dl_log_print(ILOG_INFO, "  Total Bytes: %llu", stats->total_bytes);
    dl_log_print(ILOG_INFO, "  Ring Buffer Packets: %llu", stats->ring_packets);
    dl_log_print(ILOG_INFO, "  Batch Packets: %llu", stats->batch_packets);
    dl_log_print(ILOG_INFO, "  Single Packets: %llu", stats->single_packets);
    dl_log_print(ILOG_INFO, "  Dropped Packets: %llu", stats->dropped_packets);
    dl_log_print(ILOG_INFO, "  Error Packets: %llu", stats->error_packets);
    dl_log_print(ILOG_INFO, "  Current PPS: %.2f", stats->pps);
    dl_log_print(ILOG_INFO, "  Current Mbps: %.2f", stats->mbps);
}

double pkt_perf_get_pps(pkt_perf_stats_t *stats)
{
    return stats ? stats->pps : 0.0;
}

double pkt_perf_get_mbps(pkt_perf_stats_t *stats)
{
    return stats ? stats->mbps : 0.0;
}
